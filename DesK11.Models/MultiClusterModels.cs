using System.ComponentModel;
using System.Collections.ObjectModel;

namespace DesK11.Models;

public class ClusterConnection : INotifyPropertyChanged
{
    private string _id = Guid.NewGuid().ToString();
    private string _displayName = string.Empty;
    private string _contextName = string.Empty;
    private string _kubeConfigPath = string.Empty;
    private ClusterInfo? _clusterInfo;
    private bool _isActive;
    private bool _autoConnect;
    private DateTime _createdAt = DateTime.Now;
    private DateTime? _lastUsed;
    private string _tags = string.Empty;
    private string _description = string.Empty;
    private ClusterConnectionStatus _status = ClusterConnectionStatus.Disconnected;

    public string Id
    {
        get => _id;
        set { _id = value; OnPropertyChanged(nameof(Id)); }
    }

    public string DisplayName
    {
        get => _displayName;
        set { _displayName = value; OnPropertyChanged(nameof(DisplayName)); }
    }

    public string ContextName
    {
        get => _contextName;
        set { _contextName = value; OnPropertyChanged(nameof(ContextName)); }
    }

    public string KubeConfigPath
    {
        get => _kubeConfigPath;
        set { _kubeConfigPath = value; OnPropertyChanged(nameof(KubeConfigPath)); }
    }

    public ClusterInfo? ClusterInfo
    {
        get => _clusterInfo;
        set { _clusterInfo = value; OnPropertyChanged(nameof(ClusterInfo)); }
    }

    public bool IsActive
    {
        get => _isActive;
        set { _isActive = value; OnPropertyChanged(nameof(IsActive)); }
    }

    public bool AutoConnect
    {
        get => _autoConnect;
        set { _autoConnect = value; OnPropertyChanged(nameof(AutoConnect)); }
    }

    public DateTime CreatedAt
    {
        get => _createdAt;
        set { _createdAt = value; OnPropertyChanged(nameof(CreatedAt)); }
    }

    public DateTime? LastUsed
    {
        get => _lastUsed;
        set { _lastUsed = value; OnPropertyChanged(nameof(LastUsed)); }
    }

    public string Tags
    {
        get => _tags;
        set { _tags = value; OnPropertyChanged(nameof(Tags)); }
    }

    public string Description
    {
        get => _description;
        set { _description = value; OnPropertyChanged(nameof(Description)); }
    }

    public ClusterConnectionStatus Status
    {
        get => _status;
        set { _status = value; OnPropertyChanged(nameof(Status)); }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public enum ClusterConnectionStatus
{
    Disconnected,
    Connecting,
    Connected,
    Error,
    Timeout
}

public class ClusterGroup : INotifyPropertyChanged
{
    private string _id = Guid.NewGuid().ToString();
    private string _name = string.Empty;
    private string _description = string.Empty;
    private ObservableCollection<ClusterConnection> _clusters = new();
    private bool _isExpanded = true;
    private string _color = "#0078D4";

    public string Id
    {
        get => _id;
        set { _id = value; OnPropertyChanged(nameof(Id)); }
    }

    public string Name
    {
        get => _name;
        set { _name = value; OnPropertyChanged(nameof(Name)); }
    }

    public string Description
    {
        get => _description;
        set { _description = value; OnPropertyChanged(nameof(Description)); }
    }

    public ObservableCollection<ClusterConnection> Clusters
    {
        get => _clusters;
        set { _clusters = value; OnPropertyChanged(nameof(Clusters)); }
    }

    public bool IsExpanded
    {
        get => _isExpanded;
        set { _isExpanded = value; OnPropertyChanged(nameof(IsExpanded)); }
    }

    public string Color
    {
        get => _color;
        set { _color = value; OnPropertyChanged(nameof(Color)); }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public class MultiClusterSession : INotifyPropertyChanged
{
    private ObservableCollection<ClusterConnection> _connections = new();
    private ObservableCollection<ClusterGroup> _groups = new();
    private ClusterConnection? _activeConnection;
    private string _sessionName = string.Empty;
    private DateTime _createdAt = DateTime.Now;
    private bool _autoSave = true;

    public ObservableCollection<ClusterConnection> Connections
    {
        get => _connections;
        set { _connections = value; OnPropertyChanged(nameof(Connections)); }
    }

    public ObservableCollection<ClusterGroup> Groups
    {
        get => _groups;
        set { _groups = value; OnPropertyChanged(nameof(Groups)); }
    }

    public ClusterConnection? ActiveConnection
    {
        get => _activeConnection;
        set { _activeConnection = value; OnPropertyChanged(nameof(ActiveConnection)); }
    }

    public string SessionName
    {
        get => _sessionName;
        set { _sessionName = value; OnPropertyChanged(nameof(SessionName)); }
    }

    public DateTime CreatedAt
    {
        get => _createdAt;
        set { _createdAt = value; OnPropertyChanged(nameof(CreatedAt)); }
    }

    public bool AutoSave
    {
        get => _autoSave;
        set { _autoSave = value; OnPropertyChanged(nameof(AutoSave)); }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public class ClusterHealthStatus
{
    public string ClusterId { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public DateTime LastCheck { get; set; }
    public List<string> Issues { get; set; } = new();
    public Dictionary<string, object> Metrics { get; set; } = new();
}

public class ClusterComparisonResult
{
    public string Property { get; set; } = string.Empty;
    public Dictionary<string, object> Values { get; set; } = new();
    public bool HasDifferences { get; set; }
}
