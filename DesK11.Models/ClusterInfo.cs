using System.ComponentModel;

namespace DesK11.Models;

public class ClusterInfo : INotifyPropertyChanged
{
    private string _name = string.Empty;
    private string _server = string.Empty;
    private bool _isConnected;
    private string _version = string.Empty;
    private string _status = "Disconnected";

    public string Name
    {
        get => _name;
        set
        {
            _name = value;
            OnPropertyChanged(nameof(Name));
        }
    }

    public string Server
    {
        get => _server;
        set
        {
            _server = value;
            OnPropertyChanged(nameof(Server));
        }
    }

    public bool IsConnected
    {
        get => _isConnected;
        set
        {
            _isConnected = value;
            OnPropertyChanged(nameof(IsConnected));
            Status = value ? "Connected" : "Disconnected";
        }
    }

    public string Version
    {
        get => _version;
        set
        {
            _version = value;
            OnPropertyChanged(nameof(Version));
        }
    }

    public string Status
    {
        get => _status;
        set
        {
            _status = value;
            OnPropertyChanged(nameof(Status));
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
