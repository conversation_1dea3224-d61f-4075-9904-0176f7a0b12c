using System.ComponentModel;

namespace DesK11.Models;

public class PodInfo : INotifyPropertyChanged
{
    private string _name = string.Empty;
    private string _namespace = string.Empty;
    private string _status = string.Empty;
    private string _ready = string.Empty;
    private int _restarts;
    private string _age = string.Empty;
    private string _node = string.Empty;
    private string _ip = string.Empty;

    public string Name
    {
        get => _name;
        set { _name = value; OnPropertyChanged(nameof(Name)); }
    }

    public string Namespace
    {
        get => _namespace;
        set { _namespace = value; OnPropertyChanged(nameof(Namespace)); }
    }

    public string Status
    {
        get => _status;
        set { _status = value; OnPropertyChanged(nameof(Status)); }
    }

    public string Ready
    {
        get => _ready;
        set { _ready = value; OnPropertyChanged(nameof(Ready)); }
    }

    public int Restarts
    {
        get => _restarts;
        set { _restarts = value; OnPropertyChanged(nameof(Restarts)); }
    }

    public string Age
    {
        get => _age;
        set { _age = value; OnPropertyChanged(nameof(Age)); }
    }

    public string Node
    {
        get => _node;
        set { _node = value; OnPropertyChanged(nameof(Node)); }
    }

    public string IP
    {
        get => _ip;
        set { _ip = value; OnPropertyChanged(nameof(IP)); }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public class ServiceInfo : INotifyPropertyChanged
{
    private string _name = string.Empty;
    private string _namespace = string.Empty;
    private string _type = string.Empty;
    private string _clusterIP = string.Empty;
    private string _externalIP = string.Empty;
    private string _ports = string.Empty;
    private string _age = string.Empty;

    public string Name
    {
        get => _name;
        set { _name = value; OnPropertyChanged(nameof(Name)); }
    }

    public string Namespace
    {
        get => _namespace;
        set { _namespace = value; OnPropertyChanged(nameof(Namespace)); }
    }

    public string Type
    {
        get => _type;
        set { _type = value; OnPropertyChanged(nameof(Type)); }
    }

    public string ClusterIP
    {
        get => _clusterIP;
        set { _clusterIP = value; OnPropertyChanged(nameof(ClusterIP)); }
    }

    public string ExternalIP
    {
        get => _externalIP;
        set { _externalIP = value; OnPropertyChanged(nameof(ExternalIP)); }
    }

    public string Ports
    {
        get => _ports;
        set { _ports = value; OnPropertyChanged(nameof(Ports)); }
    }

    public string Age
    {
        get => _age;
        set { _age = value; OnPropertyChanged(nameof(Age)); }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public class NodeInfo : INotifyPropertyChanged
{
    private string _name = string.Empty;
    private string _status = string.Empty;
    private string _roles = string.Empty;
    private string _age = string.Empty;
    private string _version = string.Empty;
    private string _internalIP = string.Empty;
    private string _externalIP = string.Empty;
    private string _osImage = string.Empty;
    private string _kernelVersion = string.Empty;
    private string _containerRuntime = string.Empty;

    public string Name
    {
        get => _name;
        set { _name = value; OnPropertyChanged(nameof(Name)); }
    }

    public string Status
    {
        get => _status;
        set { _status = value; OnPropertyChanged(nameof(Status)); }
    }

    public string Roles
    {
        get => _roles;
        set { _roles = value; OnPropertyChanged(nameof(Roles)); }
    }

    public string Age
    {
        get => _age;
        set { _age = value; OnPropertyChanged(nameof(Age)); }
    }

    public string Version
    {
        get => _version;
        set { _version = value; OnPropertyChanged(nameof(Version)); }
    }

    public string InternalIP
    {
        get => _internalIP;
        set { _internalIP = value; OnPropertyChanged(nameof(InternalIP)); }
    }

    public string ExternalIP
    {
        get => _externalIP;
        set { _externalIP = value; OnPropertyChanged(nameof(ExternalIP)); }
    }

    public string OSImage
    {
        get => _osImage;
        set { _osImage = value; OnPropertyChanged(nameof(OSImage)); }
    }

    public string KernelVersion
    {
        get => _kernelVersion;
        set { _kernelVersion = value; OnPropertyChanged(nameof(KernelVersion)); }
    }

    public string ContainerRuntime
    {
        get => _containerRuntime;
        set { _containerRuntime = value; OnPropertyChanged(nameof(ContainerRuntime)); }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public enum ResourceType
{
    Pod,
    Service,
    Deployment,
    Node,
    ConfigMap,
    Secret,
    Namespace,
    Ingress,
    PersistentVolume,
    PersistentVolumeClaim
}

public class LogEntry
{
    public DateTime Timestamp { get; set; }
    public LogLevel Level { get; set; }
    public string Message { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
}
