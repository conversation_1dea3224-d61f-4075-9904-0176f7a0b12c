using System;
using System.Globalization;
using System.Windows.Data;
using DesK11.Models;

namespace DesK11.UI.Converters;

public class StatusToBooleanConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is ClusterConnectionStatus status)
        {
            // Return true if status is Disconnected (can connect) or Error (can retry)
            return status == ClusterConnectionStatus.Disconnected || status == ClusterConnectionStatus.Error;
        }
        return false;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
