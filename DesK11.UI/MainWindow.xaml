<Window x:Class="DesK11.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DesK11.UI"
        mc:Ignorable="d"
        Title="DesK11 - Multi-Cluster Kubernetes Manager" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <Style x:Key="ClusterConnectionStyle" TargetType="ListBoxItem">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListBoxItem">
                        <Border x:Name="Border" Padding="8" Margin="2" BorderThickness="1" BorderBrush="LightGray" CornerRadius="4">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="{Binding DisplayName}" FontWeight="Bold" FontSize="14"/>
                                    <TextBlock Text="{Binding ContextName}" FontSize="12" Foreground="Gray"/>
                                    <TextBlock Text="{Binding Status}" FontSize="11" Foreground="{Binding Status, Converter={StaticResource StatusColorConverter}}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                                    <Button Content="Connect" Command="{Binding DataContext.ConnectToClusterCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                            CommandParameter="{Binding}" Margin="2" Padding="8,4"
                                            Visibility="{Binding Status, Converter={StaticResource DisconnectedVisibilityConverter}}"/>
                                    <Button Content="Disconnect" Command="{Binding DataContext.DisconnectFromClusterCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                            CommandParameter="{Binding}" Margin="2" Padding="8,4"
                                            Visibility="{Binding Status, Converter={StaticResource ConnectedVisibilityConverter}}"/>
                                </StackPanel>

                                <Button Grid.Column="2" Content="×" Command="{Binding DataContext.RemoveClusterCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                        CommandParameter="{Binding}" Margin="2" Padding="8,4" Background="Red" Foreground="White"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <DataTrigger Binding="{Binding IsActive}" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="Blue"/>
                                <Setter TargetName="Border" Property="BorderThickness" Value="2"/>
                            </DataTrigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="LightBlue"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <ToolBar Grid.Row="0" Height="40">
            <Button Content="Add Cluster" Command="{Binding AddClusterCommand}" Padding="10,5"/>
            <Separator/>
            <Button Content="Refresh" Command="{Binding RefreshResourcesCommand}" Padding="10,5"/>
            <Separator/>
            <ComboBox ItemsSource="{Binding Namespaces}" SelectedItem="{Binding SelectedNamespace}"
                      Width="150" Margin="5,0">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding}"/>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
            <TextBlock Text="Namespace" VerticalAlignment="Center" Margin="5,0"/>
        </ToolBar>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Cluster Connections Panel -->
            <Border Grid.Column="0" BorderBrush="Gray" BorderThickness="1" Margin="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="Cluster Connections" FontWeight="Bold" FontSize="16"
                               Margin="10" HorizontalAlignment="Center"/>

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <ListBox ItemsSource="{Binding ClusterConnections}"
                                 SelectedItem="{Binding SelectedConnection}"
                                 ItemContainerStyle="{StaticResource ClusterConnectionStyle}"
                                 Background="Transparent" BorderThickness="0"/>
                    </ScrollViewer>
                </Grid>
            </Border>

            <!-- Splitter -->
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Stretch" Background="Gray"/>

            <!-- Resource View Panel -->
            <Border Grid.Column="2" BorderBrush="Gray" BorderThickness="1" Margin="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Resource Type Tabs -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
                        <Button Content="Pods" Command="{Binding SwitchViewCommand}" CommandParameter="Pods"
                                Margin="2" Padding="10,5" IsEnabled="{Binding ActiveConnection, Converter={StaticResource NotNullConverter}}"/>
                        <Button Content="Services" Command="{Binding SwitchViewCommand}" CommandParameter="Services"
                                Margin="2" Padding="10,5" IsEnabled="{Binding ActiveConnection, Converter={StaticResource NotNullConverter}}"/>
                        <Button Content="Nodes" Command="{Binding SwitchViewCommand}" CommandParameter="Nodes"
                                Margin="2" Padding="10,5" IsEnabled="{Binding ActiveConnection, Converter={StaticResource NotNullConverter}}"/>
                    </StackPanel>

                    <!-- Resource Content -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <ContentControl>
                            <ContentControl.Style>
                                <Style TargetType="ContentControl">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding CurrentView}" Value="Pods">
                                            <Setter Property="Content">
                                                <Setter.Value>
                                                    <DataGrid ItemsSource="{Binding Pods}" AutoGenerateColumns="False" IsReadOnly="True">
                                                        <DataGrid.Columns>
                                                            <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="*"/>
                                                            <DataGridTextColumn Header="Namespace" Binding="{Binding Namespace}" Width="150"/>
                                                            <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="100"/>
                                                            <DataGridTextColumn Header="Ready" Binding="{Binding Ready}" Width="80"/>
                                                            <DataGridTextColumn Header="Restarts" Binding="{Binding Restarts}" Width="80"/>
                                                            <DataGridTextColumn Header="Age" Binding="{Binding Age}" Width="100"/>
                                                        </DataGrid.Columns>
                                                    </DataGrid>
                                                </Setter.Value>
                                            </Setter>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding CurrentView}" Value="Services">
                                            <Setter Property="Content">
                                                <Setter.Value>
                                                    <DataGrid ItemsSource="{Binding Services}" AutoGenerateColumns="False" IsReadOnly="True">
                                                        <DataGrid.Columns>
                                                            <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="*"/>
                                                            <DataGridTextColumn Header="Namespace" Binding="{Binding Namespace}" Width="150"/>
                                                            <DataGridTextColumn Header="Type" Binding="{Binding Type}" Width="120"/>
                                                            <DataGridTextColumn Header="Cluster IP" Binding="{Binding ClusterIP}" Width="120"/>
                                                            <DataGridTextColumn Header="External IP" Binding="{Binding ExternalIP}" Width="120"/>
                                                            <DataGridTextColumn Header="Ports" Binding="{Binding Ports}" Width="150"/>
                                                        </DataGrid.Columns>
                                                    </DataGrid>
                                                </Setter.Value>
                                            </Setter>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding CurrentView}" Value="Nodes">
                                            <Setter Property="Content">
                                                <Setter.Value>
                                                    <DataGrid ItemsSource="{Binding Nodes}" AutoGenerateColumns="False" IsReadOnly="True">
                                                        <DataGrid.Columns>
                                                            <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="*"/>
                                                            <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="100"/>
                                                            <DataGridTextColumn Header="Roles" Binding="{Binding Roles}" Width="150"/>
                                                            <DataGridTextColumn Header="Version" Binding="{Binding Version}" Width="120"/>
                                                            <DataGridTextColumn Header="OS" Binding="{Binding OperatingSystem}" Width="100"/>
                                                        </DataGrid.Columns>
                                                    </DataGrid>
                                                </Setter.Value>
                                            </Setter>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ContentControl.Style>
                        </ContentControl>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Height="25">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="Active Cluster: "/>
                    <TextBlock Text="{Binding ActiveConnection.DisplayName}" FontWeight="Bold"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
