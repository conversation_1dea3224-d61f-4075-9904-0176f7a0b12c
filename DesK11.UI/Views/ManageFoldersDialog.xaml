<Window x:Class="DesK11.UI.Views.ManageFoldersDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Manage Cluster Folders" 
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10,6"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>
        
        <Style TargetType="TextBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="6"/>
        </Style>
        
        <Style TargetType="Label">
            <Setter Property="Margin" Value="5,5,5,0"/>
        </Style>
    </Window.Resources>

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Manage Cluster Folders" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,15"/>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- Folders List -->
            <GroupBox Grid.Column="0" Header="Existing Folders" Margin="0,0,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <ListBox Grid.Row="0" ItemsSource="{Binding Folders}"
                             SelectedItem="{Binding SelectedFolder}"
                             Margin="5">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Ellipse Width="12" Height="12" 
                                             Fill="{Binding Color}" 
                                             Margin="0,0,8,0"/>
                                    <StackPanel>
                                        <TextBlock Text="{Binding Name}" FontWeight="SemiBold"/>
                                        <TextBlock Text="{Binding Description}" 
                                                   FontSize="11" Foreground="Gray"
                                                   Visibility="{Binding Description, Converter={StaticResource StringToVisibilityConverter}}"/>
                                        <TextBlock FontSize="10" Foreground="DarkGray">
                                            <Run Text="{Binding Clusters.Count}"/>
                                            <Run Text="clusters"/>
                                        </TextBlock>
                                    </StackPanel>
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                    
                    <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
                        <Button Content="Delete Folder" 
                                Command="{Binding DeleteFolderCommand}"
                                IsEnabled="{Binding CanDeleteFolder}"/>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- Splitter -->
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Center" 
                          VerticalAlignment="Stretch" Background="LightGray"/>

            <!-- Folder Details/Create -->
            <GroupBox Grid.Column="2" Header="Folder Details" Margin="10,0,0,0">
                <StackPanel>
                    <Label Content="Name"/>
                    <TextBox Text="{Binding FolderName, UpdateSourceTrigger=PropertyChanged}"/>
                    
                    <Label Content="Description"/>
                    <TextBox Text="{Binding FolderDescription, UpdateSourceTrigger=PropertyChanged}"
                             Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                             VerticalScrollBarVisibility="Auto"/>
                    
                    <Label Content="Color"/>
                    <ComboBox ItemsSource="{Binding AvailableColors}"
                              SelectedItem="{Binding SelectedColor}"
                              Height="30" Margin="5">
                        <ComboBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Rectangle Width="20" Height="15" 
                                               Fill="{Binding}" 
                                               Stroke="Gray" StrokeThickness="1"
                                               Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </ComboBox.ItemTemplate>
                    </ComboBox>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,15,0,0">
                        <Button Content="Create Folder" 
                                Command="{Binding CreateFolderCommand}"
                                IsEnabled="{Binding CanCreateFolder}"
                                Visibility="{Binding IsCreatingNewFolder, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        <Button Content="Update Folder" 
                                Command="{Binding UpdateFolderCommand}"
                                IsEnabled="{Binding CanUpdateFolder}"
                                Visibility="{Binding IsEditingExistingFolder, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        <Button Content="Clear" 
                                Command="{Binding ClearFormCommand}"/>
                    </StackPanel>
                    
                    <!-- Clusters in Selected Folder -->
                    <Label Content="Clusters in this folder" Margin="5,20,5,5"/>
                    <ListBox ItemsSource="{Binding ClustersInSelectedFolder}"
                             Height="120" Margin="5">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Ellipse Width="8" Height="8" 
                                             Fill="{Binding Status, Converter={StaticResource StatusToColorConverter}}" 
                                             Margin="0,0,6,0"/>
                                    <TextBlock Text="{Binding DisplayName}"/>
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </StackPanel>
            </GroupBox>
        </Grid>

        <!-- Bottom Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button Content="Close" 
                    Command="{Binding CloseCommand}"
                    IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
