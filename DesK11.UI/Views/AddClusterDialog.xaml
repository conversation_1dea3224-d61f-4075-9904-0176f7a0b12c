<Window x:Class="DesK11.UI.Views.AddClusterDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Add Kubernetes Cluster" 
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <Style TargetType="TextBox">
            <Setter Property="Margin" Value="0,5,0,10"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
        </Style>
        
        <Style TargetType="ComboBox">
            <Setter Property="Margin" Value="0,5,0,10"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
        </Style>
        
        <Style TargetType="Label">
            <Setter Property="Margin" Value="0,10,0,0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
        
        <Style TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Add New Kubernetes Cluster" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Basic Information -->
                <GroupBox Header="Basic Information" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <Label Content="Display Name *"/>
                        <TextBox Text="{Binding DisplayName, UpdateSourceTrigger=PropertyChanged}"
                                 ToolTip="A friendly name for this cluster connection"/>
                        
                        <Label Content="Description"/>
                        <TextBox Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                 Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"
                                 ToolTip="Optional description for this cluster"/>
                    </StackPanel>
                </GroupBox>

                <!-- Connection Settings -->
                <GroupBox Header="Connection Settings" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <TabControl Height="200" Margin="0,0,0,10">
                            <!-- File Path Tab -->
                            <TabItem Header="Browse File">
                                <StackPanel Margin="10">
                                    <Label Content="Kubeconfig File Path *"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBox Grid.Column="0" Text="{Binding KubeConfigPath, UpdateSourceTrigger=PropertyChanged}"
                                                 ToolTip="Path to the kubeconfig file"/>
                                        <Button Grid.Column="1" Content="Browse..."
                                                Command="{Binding BrowseKubeConfigCommand}"
                                                Margin="5,0,0,0" Padding="10,6"/>
                                    </Grid>
                                    <TextBlock Text="Browse and select your kubeconfig file from the file system."
                                               Foreground="Gray" FontSize="11" Margin="0,5,0,0"/>
                                </StackPanel>
                            </TabItem>

                            <!-- Paste Content Tab -->
                            <TabItem Header="Paste Content">
                                <StackPanel Margin="10">
                                    <Label Content="Kubeconfig Content *"/>
                                    <TextBox Text="{Binding KubeConfigContent, UpdateSourceTrigger=PropertyChanged}"
                                             Height="120" TextWrapping="Wrap" AcceptsReturn="True"
                                             VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto"
                                             FontFamily="Consolas" FontSize="11"
                                             ToolTip="Paste your kubeconfig YAML content here"/>
                                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                        <Button Content="Validate" Command="{Binding ValidateKubeConfigContentCommand}"
                                                Padding="10,4" Margin="0,0,5,0"/>
                                        <Button Content="Clear" Command="{Binding ClearKubeConfigContentCommand}"
                                                Padding="10,4"/>
                                    </StackPanel>
                                    <TextBlock Text="Paste your kubeconfig YAML content directly. Click Validate to parse contexts."
                                               Foreground="Gray" FontSize="11" Margin="0,5,0,0"/>
                                </StackPanel>
                            </TabItem>
                        </TabControl>

                        <Label Content="Context"/>
                        <ComboBox ItemsSource="{Binding AvailableContexts}"
                                  SelectedItem="{Binding SelectedContext}"
                                  DisplayMemberPath="Name"
                                  IsEnabled="{Binding HasValidKubeConfig}"
                                  ToolTip="Kubernetes context to use from the kubeconfig file"/>

                        <CheckBox Content="Auto-connect on startup"
                                  IsChecked="{Binding AutoConnect}"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </GroupBox>

                <!-- Organization -->
                <GroupBox Header="Organization" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <Label Content="Folder"/>
                        <ComboBox ItemsSource="{Binding AvailableFolders}"
                                  SelectedItem="{Binding SelectedFolder}"
                                  DisplayMemberPath="Name"
                                  IsEditable="True"
                                  Text="{Binding FolderName, UpdateSourceTrigger=PropertyChanged}"
                                  ToolTip="Select existing folder or type new folder name"/>
                        
                        <Label Content="Tags"/>
                        <TextBox Text="{Binding Tags, UpdateSourceTrigger=PropertyChanged}"
                                 ToolTip="Comma-separated tags for organizing clusters"/>
                    </StackPanel>
                </GroupBox>

                <!-- Advanced Settings -->
                <Expander Header="Advanced Settings" IsExpanded="False">
                    <StackPanel Margin="10">
                        <Label Content="Connection Timeout (seconds)"/>
                        <TextBox Text="{Binding ConnectionTimeout, UpdateSourceTrigger=PropertyChanged}"
                                 ToolTip="Timeout for cluster connection attempts"/>
                        
                        <Label Content="Default Namespace"/>
                        <TextBox Text="{Binding DefaultNamespace, UpdateSourceTrigger=PropertyChanged}"
                                 ToolTip="Default namespace to use when connecting"/>
                        
                        <CheckBox Content="Skip TLS verification" 
                                  IsChecked="{Binding SkipTlsVerification}"
                                  Margin="0,10,0,0"
                                  ToolTip="Skip TLS certificate verification (not recommended for production)"/>
                    </StackPanel>
                </Expander>
            </StackPanel>
        </ScrollViewer>

        <!-- Status Section -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="1"
                Margin="0,10,0,0" Padding="10" CornerRadius="4"
                Visibility="{Binding ShowStatus, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel>
                <TextBlock Text="Status:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBlock Text="{Binding TestResult}" TextWrapping="Wrap"
                           Foreground="{Binding TestResultColor}"/>

                <!-- Progress indicator -->
                <StackPanel Orientation="Horizontal" Margin="0,5,0,0"
                            Visibility="{Binding IsOperationInProgress, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="●" FontSize="16" Margin="0,0,5,0">
                        <TextBlock.RenderTransform>
                            <RotateTransform x:Name="SpinnerTransform" CenterX="8" CenterY="8"/>
                        </TextBlock.RenderTransform>
                        <TextBlock.Triggers>
                            <EventTrigger RoutedEvent="Loaded">
                                <BeginStoryboard>
                                    <Storyboard RepeatBehavior="Forever">
                                        <DoubleAnimation Storyboard.TargetName="SpinnerTransform"
                                                         Storyboard.TargetProperty="Angle"
                                                         From="0" To="360" Duration="0:0:1"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </EventTrigger>
                        </TextBlock.Triggers>
                    </TextBlock>
                    <TextBlock Text="{Binding OperationStatus}" VerticalAlignment="Center"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal"
                    HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button Content="Test Connection"
                    Command="{Binding TestConnectionCommand}"
                    IsEnabled="{Binding CanTestConnection}"
                    Padding="15,8" Margin="0,0,10,0"/>
            <Button Content="Add Cluster"
                    Command="{Binding AddClusterCommand}"
                    IsEnabled="{Binding CanAddCluster}"
                    IsDefault="True"
                    Padding="15,8" Margin="0,0,10,0"/>
            <Button Content="Cancel"
                    Command="{Binding CancelCommand}"
                    IsCancel="True"
                    Padding="15,8"/>
        </StackPanel>
    </Grid>
</Window>
