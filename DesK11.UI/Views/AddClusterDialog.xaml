<Window x:Class="DesK11.UI.Views.AddClusterDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Add Kubernetes Cluster" 
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <Window.Resources>
        <Style TargetType="TextBox">
            <Setter Property="Margin" Value="0,5,0,10"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
        </Style>
        
        <Style TargetType="ComboBox">
            <Setter Property="Margin" Value="0,5,0,10"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
        </Style>
        
        <Style TargetType="Label">
            <Setter Property="Margin" Value="0,10,0,0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
        
        <Style TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Add New Kubernetes Cluster" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Basic Information -->
                <GroupBox Header="Basic Information" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <Label Content="Display Name *"/>
                        <TextBox Text="{Binding DisplayName, UpdateSourceTrigger=PropertyChanged}"
                                 ToolTip="A friendly name for this cluster connection"/>
                        
                        <Label Content="Description"/>
                        <TextBox Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                 Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"
                                 ToolTip="Optional description for this cluster"/>
                    </StackPanel>
                </GroupBox>

                <!-- Connection Settings -->
                <GroupBox Header="Connection Settings" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <Label Content="Kubeconfig File Path *"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox Grid.Column="0" Text="{Binding KubeConfigPath, UpdateSourceTrigger=PropertyChanged}"
                                     ToolTip="Path to the kubeconfig file"/>
                            <Button Grid.Column="1" Content="Browse..." 
                                    Command="{Binding BrowseKubeConfigCommand}"
                                    Margin="5,5,0,10" Padding="10,6"/>
                        </Grid>
                        
                        <Label Content="Context"/>
                        <ComboBox ItemsSource="{Binding AvailableContexts}"
                                  SelectedItem="{Binding SelectedContext}"
                                  DisplayMemberPath="Name"
                                  IsEnabled="{Binding HasValidKubeConfig}"
                                  ToolTip="Kubernetes context to use from the kubeconfig file"/>
                        
                        <CheckBox Content="Auto-connect on startup" 
                                  IsChecked="{Binding AutoConnect}"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </GroupBox>

                <!-- Organization -->
                <GroupBox Header="Organization" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <Label Content="Folder"/>
                        <ComboBox ItemsSource="{Binding AvailableFolders}"
                                  SelectedItem="{Binding SelectedFolder}"
                                  DisplayMemberPath="Name"
                                  IsEditable="True"
                                  Text="{Binding FolderName, UpdateSourceTrigger=PropertyChanged}"
                                  ToolTip="Select existing folder or type new folder name"/>
                        
                        <Label Content="Tags"/>
                        <TextBox Text="{Binding Tags, UpdateSourceTrigger=PropertyChanged}"
                                 ToolTip="Comma-separated tags for organizing clusters"/>
                    </StackPanel>
                </GroupBox>

                <!-- Advanced Settings -->
                <Expander Header="Advanced Settings" IsExpanded="False">
                    <StackPanel Margin="10">
                        <Label Content="Connection Timeout (seconds)"/>
                        <TextBox Text="{Binding ConnectionTimeout, UpdateSourceTrigger=PropertyChanged}"
                                 ToolTip="Timeout for cluster connection attempts"/>
                        
                        <Label Content="Default Namespace"/>
                        <TextBox Text="{Binding DefaultNamespace, UpdateSourceTrigger=PropertyChanged}"
                                 ToolTip="Default namespace to use when connecting"/>
                        
                        <CheckBox Content="Skip TLS verification" 
                                  IsChecked="{Binding SkipTlsVerification}"
                                  Margin="0,10,0,0"
                                  ToolTip="Skip TLS certificate verification (not recommended for production)"/>
                    </StackPanel>
                </Expander>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="Test Connection" 
                    Command="{Binding TestConnectionCommand}"
                    IsEnabled="{Binding CanTestConnection}"/>
            <Button Content="Add Cluster" 
                    Command="{Binding AddClusterCommand}"
                    IsEnabled="{Binding CanAddCluster}"
                    IsDefault="True"/>
            <Button Content="Cancel" 
                    Command="{Binding CancelCommand}"
                    IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
