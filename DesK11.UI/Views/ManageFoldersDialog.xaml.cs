using System.Windows;
using DesK11.UI.ViewModels;

namespace DesK11.UI.Views;

/// <summary>
/// Interaction logic for ManageFoldersDialog.xaml
/// </summary>
public partial class ManageFoldersDialog : Window
{
    public ManageFoldersDialog()
    {
        InitializeComponent();
    }

    public ManageFoldersDialog(ManageFoldersViewModel viewModel) : this()
    {
        DataContext = viewModel;
        
        // Subscribe to ViewModel events
        viewModel.DialogClosed += OnDialogClosed;
    }

    private void OnDialogClosed(object? sender, EventArgs e)
    {
        Close();
    }

    protected override void OnClosed(EventArgs e)
    {
        if (DataContext is ManageFoldersViewModel viewModel)
        {
            viewModel.DialogClosed -= OnDialogClosed;
        }
        base.OnClosed(e);
    }
}
