using System.Windows;
using DesK11.UI.ViewModels;

namespace DesK11.UI.Views;

/// <summary>
/// Interaction logic for AddClusterDialog.xaml
/// </summary>
public partial class AddClusterDialog : Window
{
    public AddClusterDialog()
    {
        InitializeComponent();
    }

    public AddClusterDialog(AddClusterViewModel viewModel) : this()
    {
        DataContext = viewModel;
        
        // Subscribe to ViewModel events
        viewModel.ClusterAdded += OnClusterAdded;
        viewModel.DialogCancelled += OnDialogCancelled;
    }

    private void OnClusterAdded(object? sender, EventArgs e)
    {
        DialogResult = true;
        Close();
    }

    private void OnDialogCancelled(object? sender, EventArgs e)
    {
        DialogResult = false;
        Close();
    }

    protected override void OnClosed(EventArgs e)
    {
        if (DataContext is AddClusterViewModel viewModel)
        {
            viewModel.ClusterAdded -= OnClusterAdded;
            viewModel.DialogCancelled -= OnDialogCancelled;
        }
        base.OnClosed(e);
    }
}
