using System.Windows;
using DesK11.Core.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using DesK11.Core.Services;
using DesK11.Kubernetes;
using DesK11.UI.ViewModels;

namespace DesK11.UI;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Register services
                services.AddSingleton<ILogService, LogService>();
                services.AddSingleton<IConfigurationService, ConfigurationService>();
                services.AddSingleton<IKubernetesService, KubernetesService>();
                services.AddSingleton<IMultiClusterService, MultiClusterService>();

                // Register ViewModels
                services.AddTransient<MainViewModel>();

                // Register Windows
                services.AddTransient<MainWindow>();
            })
            .Build();

        await _host.StartAsync();

        // Get the main window from DI container
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }

        base.OnExit(e);
    }
}

