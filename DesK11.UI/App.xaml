<Application x:Class="DesK11.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DesK11.UI"
             xmlns:converters="clr-namespace:DesK11.UI.Converters">
    <Application.Resources>
        <!-- Value Converters -->
        <converters:StatusColorConverter x:Key="StatusColorConverter"/>
        <converters:ConnectedVisibilityConverter x:Key="ConnectedVisibilityConverter"/>
        <converters:DisconnectedVisibilityConverter x:Key="DisconnectedVisibilityConverter"/>
        <converters:NotNullConverter x:Key="NotNullConverter"/>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:ClusterHealthToColorConverter x:Key="ClusterHealthToColorConverter"/>
        <converters:DateTimeToStringConverter x:Key="DateTimeToStringConverter"/>
        <converters:CountToStringConverter x:Key="CountToStringConverter"/>
        <converters:StringArrayToStringConverter x:Key="StringArrayToStringConverter"/>

        <!-- Styles -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="5"/>
        </Style>

        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="2"/>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>

        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="Red"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="Blue"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
    </Application.Resources>
</Application>
