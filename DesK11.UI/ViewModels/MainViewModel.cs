using System.Collections.ObjectModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DesK11.Core.Interfaces;
using DesK11.Models;

namespace DesK11.UI.ViewModels;

public partial class MainViewModel : ObservableObject
{
    private readonly IMultiClusterService _multiClusterService;
    private readonly IKubernetesService _kubernetesService;
    private readonly ILogService _logService;

    [ObservableProperty]
    private ObservableCollection<ClusterConnection> _clusterConnections = new();

    [ObservableProperty]
    private ObservableCollection<ClusterGroup> _clusterGroups = new();

    [ObservableProperty]
    private ObservableCollection<ClusterConnection> _ungroupedConnections = new();

    [ObservableProperty]
    private ClusterConnection? _selectedConnection;

    [ObservableProperty]
    private ClusterConnection? _activeConnection;

    [ObservableProperty]
    private ObservableCollection<PodInfo> _pods = new();

    [ObservableProperty]
    private ObservableCollection<ServiceInfo> _services = new();

    [ObservableProperty]
    private ObservableCollection<NodeInfo> _nodes = new();

    [ObservableProperty]
    private ObservableCollection<string> _namespaces = new();

    [ObservableProperty]
    private string? _selectedNamespace;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _currentView = "Pods";

    public ICommand ConnectToClusterCommand { get; }
    public ICommand DisconnectFromClusterCommand { get; }
    public ICommand AddClusterCommand { get; }
    public ICommand RemoveClusterCommand { get; }
    public ICommand RefreshResourcesCommand { get; }
    public ICommand SwitchViewCommand { get; }
    public ICommand ManageFoldersCommand { get; }
    public ICommand SwitchNamespaceCommand { get; }

    public MainViewModel(
        IMultiClusterService multiClusterService,
        IKubernetesService kubernetesService,
        ILogService logService)
    {
        _multiClusterService = multiClusterService;
        _kubernetesService = kubernetesService;
        _logService = logService;

        // Initialize commands
        ConnectToClusterCommand = new AsyncRelayCommand<ClusterConnection>(ConnectToClusterAsync);
        DisconnectFromClusterCommand = new AsyncRelayCommand<ClusterConnection>(DisconnectFromClusterAsync);
        AddClusterCommand = new AsyncRelayCommand(AddClusterAsync);
        RemoveClusterCommand = new AsyncRelayCommand<ClusterConnection>(RemoveClusterAsync);
        ManageFoldersCommand = new AsyncRelayCommand(ManageFoldersAsync);
        RefreshResourcesCommand = new AsyncRelayCommand(RefreshResourcesAsync);
        SwitchViewCommand = new RelayCommand<string>(SwitchView);
        SwitchNamespaceCommand = new AsyncRelayCommand<string>(SwitchNamespaceAsync);

        // Subscribe to events
        _multiClusterService.ConnectionAdded += OnConnectionAdded;
        _multiClusterService.ConnectionRemoved += OnConnectionRemoved;
        _multiClusterService.ConnectionStatusChanged += OnConnectionStatusChanged;
        _multiClusterService.ActiveConnectionChanged += OnActiveConnectionChanged;

        _kubernetesService.ClusterConnected += OnClusterConnected;
        _kubernetesService.ClusterDisconnected += OnClusterDisconnected;
        _kubernetesService.ErrorOccurred += OnErrorOccurred;

        // Load initial data
        _ = LoadInitialDataAsync();
    }

    private async Task LoadInitialDataAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "Loading cluster connections...";

            await LoadClustersAsync();

            ActiveConnection = _multiClusterService.ActiveConnection;
            
            if (ActiveConnection != null)
            {
                await RefreshResourcesAsync();
            }

            StatusMessage = "Ready";
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to load initial data", ex);
            StatusMessage = $"Error: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadClustersAsync()
    {
        try
        {
            // Load all connections
            var connections = await _multiClusterService.GetConnectionsAsync();
            ClusterConnections.Clear();
            foreach (var connection in connections)
            {
                ClusterConnections.Add(connection);
            }

            // Load all groups
            var groups = await _multiClusterService.GetGroupsAsync();
            ClusterGroups.Clear();
            foreach (var group in groups)
            {
                ClusterGroups.Add(group);
            }

            // Populate ungrouped connections
            UngroupedConnections.Clear();
            var groupedConnectionIds = groups.SelectMany(g => g.Clusters.Select(c => c.Id)).ToHashSet();

            foreach (var connection in connections)
            {
                if (!groupedConnectionIds.Contains(connection.Id))
                {
                    UngroupedConnections.Add(connection);
                }
            }
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to load clusters", ex);
            throw;
        }
    }

    private async Task ConnectToClusterAsync(ClusterConnection? connection)
    {
        if (connection == null) return;

        try
        {
            IsLoading = true;
            StatusMessage = $"Connecting to {connection.DisplayName}...";

            var success = await _multiClusterService.ConnectToClusterAsync(connection.Id);
            if (success)
            {
                StatusMessage = $"Connected to {connection.DisplayName}";
                await RefreshResourcesAsync();
            }
            else
            {
                StatusMessage = $"Failed to connect to {connection.DisplayName}";
            }
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to connect to cluster: {connection.DisplayName}", ex);
            StatusMessage = $"Error connecting: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task DisconnectFromClusterAsync(ClusterConnection? connection)
    {
        if (connection == null) return;

        try
        {
            IsLoading = true;
            StatusMessage = $"Disconnecting from {connection.DisplayName}...";

            var success = await _multiClusterService.DisconnectFromClusterAsync(connection.Id);
            if (success)
            {
                StatusMessage = $"Disconnected from {connection.DisplayName}";
                
                // Clear resources if this was the active connection
                if (connection == ActiveConnection)
                {
                    Pods.Clear();
                    Services.Clear();
                    Nodes.Clear();
                    Namespaces.Clear();
                }
            }
            else
            {
                StatusMessage = $"Failed to disconnect from {connection.DisplayName}";
            }
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to disconnect from cluster: {connection.DisplayName}", ex);
            StatusMessage = $"Error disconnecting: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task AddClusterAsync()
    {
        try
        {
            var addClusterViewModel = new AddClusterViewModel(
                _multiClusterService,
                _kubernetesService,
                _logService);

            var dialog = new Views.AddClusterDialog(addClusterViewModel);
            var result = dialog.ShowDialog();

            if (result == true)
            {
                // Refresh the cluster connections and groups
                await LoadClustersAsync();
                StatusMessage = "Cluster added successfully";
            }
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to add cluster", ex);
            StatusMessage = $"Error adding cluster: {ex.Message}";
        }
    }

    private async Task ManageFoldersAsync()
    {
        try
        {
            var manageFoldersViewModel = new ManageFoldersViewModel(
                _multiClusterService,
                _logService);

            var dialog = new Views.ManageFoldersDialog(manageFoldersViewModel);
            dialog.ShowDialog();

            // Refresh the cluster groups after managing folders
            await LoadClustersAsync();
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to manage folders", ex);
            StatusMessage = $"Error managing folders: {ex.Message}";
        }
    }

    private async Task RemoveClusterAsync(ClusterConnection? connection)
    {
        if (connection == null) return;

        try
        {
            var success = await _multiClusterService.RemoveConnectionAsync(connection.Id);
            if (success)
            {
                ClusterConnections.Remove(connection);
                StatusMessage = $"Removed cluster: {connection.DisplayName}";
            }
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to remove cluster: {connection.DisplayName}", ex);
            StatusMessage = $"Error removing cluster: {ex.Message}";
        }
    }

    private async Task RefreshResourcesAsync()
    {
        if (ActiveConnection?.Status != ClusterConnectionStatus.Connected)
            return;

        try
        {
            IsLoading = true;
            StatusMessage = "Refreshing resources...";

            // Load namespaces
            var namespaces = await _kubernetesService.GetNamespacesAsync();
            Namespaces.Clear();
            foreach (var ns in namespaces)
            {
                Namespaces.Add(ns);
            }

            // Load resources based on current view
            switch (CurrentView)
            {
                case "Pods":
                    await LoadPodsAsync();
                    break;
                case "Services":
                    await LoadServicesAsync();
                    break;
                case "Nodes":
                    await LoadNodesAsync();
                    break;
            }

            StatusMessage = "Resources refreshed";
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to refresh resources", ex);
            StatusMessage = $"Error refreshing: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadPodsAsync()
    {
        var pods = await _kubernetesService.GetPodsAsync(SelectedNamespace);
        Pods.Clear();
        foreach (var pod in pods)
        {
            Pods.Add(pod);
        }
    }

    private async Task LoadServicesAsync()
    {
        var services = await _kubernetesService.GetServicesAsync(SelectedNamespace);
        Services.Clear();
        foreach (var service in services)
        {
            Services.Add(service);
        }
    }

    private async Task LoadNodesAsync()
    {
        var nodes = await _kubernetesService.GetNodesAsync();
        Nodes.Clear();
        foreach (var node in nodes)
        {
            Nodes.Add(node);
        }
    }

    private void SwitchView(string? viewName)
    {
        if (string.IsNullOrEmpty(viewName)) return;
        
        CurrentView = viewName;
        _ = RefreshResourcesAsync();
    }

    private async Task SwitchNamespaceAsync(string? namespaceName)
    {
        SelectedNamespace = namespaceName;
        await RefreshResourcesAsync();
    }

    // Event handlers
    private void OnConnectionAdded(object? sender, ClusterConnection connection)
    {
        App.Current.Dispatcher.Invoke(() => ClusterConnections.Add(connection));
    }

    private void OnConnectionRemoved(object? sender, ClusterConnection connection)
    {
        App.Current.Dispatcher.Invoke(() => ClusterConnections.Remove(connection));
    }

    private void OnConnectionStatusChanged(object? sender, ClusterConnection connection)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            // The connection object should already be updated via binding
            StatusMessage = $"Connection {connection.DisplayName} status: {connection.Status}";
        });
    }

    private void OnActiveConnectionChanged(object? sender, ClusterConnection connection)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            ActiveConnection = connection;
            _ = RefreshResourcesAsync();
        });
    }

    private void OnClusterConnected(object? sender, ClusterInfo clusterInfo)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            StatusMessage = $"Connected to cluster: {clusterInfo.Name}";
        });
    }

    private void OnClusterDisconnected(object? sender, EventArgs e)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            StatusMessage = "Disconnected from cluster";
            Pods.Clear();
            Services.Clear();
            Nodes.Clear();
            Namespaces.Clear();
        });
    }

    private void OnErrorOccurred(object? sender, string error)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            StatusMessage = $"Error: {error}";
        });
    }
}
