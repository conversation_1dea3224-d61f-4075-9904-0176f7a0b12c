using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DesK11.Core.Interfaces;
using DesK11.Models;

namespace DesK11.UI.ViewModels;

public partial class ManageFoldersViewModel : ObservableValidator
{
    private readonly IMultiClusterService _multiClusterService;
    private readonly ILogService _logService;

    [ObservableProperty]
    private ObservableCollection<ClusterGroup> _folders = new();

    [ObservableProperty]
    private ClusterGroup? _selectedFolder;

    [ObservableProperty]
    [Required(ErrorMessage = "Folder name is required")]
    [MinLength(1, ErrorMessage = "Folder name cannot be empty")]
    private string _folderName = "";

    [ObservableProperty]
    private string _folderDescription = "";

    [ObservableProperty]
    private string _selectedColor = "#0078D4";

    [ObservableProperty]
    private ObservableCollection<ClusterConnection> _clustersInSelectedFolder = new();

    [ObservableProperty]
    private ObservableCollection<string> _availableColors = new()
    {
        "#0078D4", // Blue
        "#107C10", // Green
        "#FF8C00", // Orange
        "#E81123", // Red
        "#5C2D91", // Purple
        "#00BCF2", // Cyan
        "#FFB900", // Yellow
        "#00CC6A", // Lime
        "#FF69B4", // Pink
        "#8B4513"  // Brown
    };

    public ICommand CreateFolderCommand { get; }
    public ICommand UpdateFolderCommand { get; }
    public ICommand DeleteFolderCommand { get; }
    public ICommand ClearFormCommand { get; }
    public ICommand CloseCommand { get; }

    public bool CanCreateFolder => !HasErrors && !string.IsNullOrWhiteSpace(FolderName) && IsCreatingNewFolder;
    public bool CanUpdateFolder => !HasErrors && !string.IsNullOrWhiteSpace(FolderName) && IsEditingExistingFolder;
    public bool CanDeleteFolder => SelectedFolder != null;
    public bool IsCreatingNewFolder => SelectedFolder == null;
    public bool IsEditingExistingFolder => SelectedFolder != null;

    public event EventHandler? DialogClosed;

    public ManageFoldersViewModel(
        IMultiClusterService multiClusterService,
        ILogService logService)
    {
        _multiClusterService = multiClusterService;
        _logService = logService;

        CreateFolderCommand = new AsyncRelayCommand(CreateFolderAsync);
        UpdateFolderCommand = new AsyncRelayCommand(UpdateFolderAsync);
        DeleteFolderCommand = new AsyncRelayCommand(DeleteFolderAsync);
        ClearFormCommand = new RelayCommand(ClearForm);
        CloseCommand = new RelayCommand(Close);

        LoadFolders();
    }

    partial void OnSelectedFolderChanged(ClusterGroup? value)
    {
        if (value != null)
        {
            FolderName = value.Name;
            FolderDescription = value.Description;
            SelectedColor = value.Color;
            
            ClustersInSelectedFolder.Clear();
            foreach (var cluster in value.Clusters)
            {
                ClustersInSelectedFolder.Add(cluster);
            }
        }
        else
        {
            ClustersInSelectedFolder.Clear();
        }

        OnPropertyChanged(nameof(CanCreateFolder));
        OnPropertyChanged(nameof(CanUpdateFolder));
        OnPropertyChanged(nameof(CanDeleteFolder));
        OnPropertyChanged(nameof(IsCreatingNewFolder));
        OnPropertyChanged(nameof(IsEditingExistingFolder));
    }

    partial void OnFolderNameChanged(string value)
    {
        ValidateAllProperties();
        OnPropertyChanged(nameof(CanCreateFolder));
        OnPropertyChanged(nameof(CanUpdateFolder));
    }

    private async Task CreateFolderAsync()
    {
        if (!CanCreateFolder) return;

        try
        {
            ValidateAllProperties();
            if (HasErrors) return;

            var folder = await _multiClusterService.CreateGroupAsync(FolderName, FolderDescription);
            folder.Color = SelectedColor;
            
            await _multiClusterService.UpdateGroupAsync(folder);

            Folders.Add(folder);
            ClearForm();

            _logService.LogInfo($"Successfully created folder: {FolderName}");
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to create folder: {FolderName}", ex);
        }
    }

    private async Task UpdateFolderAsync()
    {
        if (!CanUpdateFolder || SelectedFolder == null) return;

        try
        {
            ValidateAllProperties();
            if (HasErrors) return;

            SelectedFolder.Name = FolderName;
            SelectedFolder.Description = FolderDescription;
            SelectedFolder.Color = SelectedColor;

            await _multiClusterService.UpdateGroupAsync(SelectedFolder);

            _logService.LogInfo($"Successfully updated folder: {FolderName}");
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to update folder: {FolderName}", ex);
        }
    }

    private async Task DeleteFolderAsync()
    {
        if (!CanDeleteFolder || SelectedFolder == null) return;

        try
        {
            var result = System.Windows.MessageBox.Show(
                $"Are you sure you want to delete the folder '{SelectedFolder.Name}'?\n\n" +
                $"This will move {SelectedFolder.Clusters.Count} cluster(s) to the root level.",
                "Confirm Delete",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result != System.Windows.MessageBoxResult.Yes)
                return;

            var success = await _multiClusterService.DeleteGroupAsync(SelectedFolder.Id);
            if (success)
            {
                Folders.Remove(SelectedFolder);
                ClearForm();
                _logService.LogInfo($"Successfully deleted folder: {SelectedFolder.Name}");
            }
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to delete folder: {SelectedFolder?.Name}", ex);
        }
    }

    private void ClearForm()
    {
        SelectedFolder = null;
        FolderName = "";
        FolderDescription = "";
        SelectedColor = "#0078D4";
        ClearErrors();
    }

    private void Close()
    {
        DialogClosed?.Invoke(this, EventArgs.Empty);
    }

    private async void LoadFolders()
    {
        try
        {
            var groups = await _multiClusterService.GetGroupsAsync();
            Folders.Clear();
            
            foreach (var group in groups)
            {
                Folders.Add(group);
            }
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to load folders", ex);
        }
    }
}
