using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DesK11.Core.Interfaces;
using DesK11.Models;
using Microsoft.Win32;

namespace DesK11.UI.ViewModels;

public partial class AddClusterViewModel : ObservableValidator
{
    private readonly IMultiClusterService _multiClusterService;
    private readonly IKubernetesService _kubernetesService;
    private readonly ILogService _logService;

    [ObservableProperty]
    [Required(ErrorMessage = "Display name is required")]
    [MinLength(1, ErrorMessage = "Display name cannot be empty")]
    private string _displayName = "";

    [ObservableProperty]
    private string _description = "";

    [ObservableProperty]
    private string _kubeConfigPath = "";

    [ObservableProperty]
    private string _kubeConfigContent = "";

    [ObservableProperty]
    private ContextInfo? _selectedContext;

    [ObservableProperty]
    private ClusterGroup? _selectedFolder;

    [ObservableProperty]
    private string _folderName = "";

    [ObservableProperty]
    private string _tags = "";

    [ObservableProperty]
    private bool _autoConnect;

    [ObservableProperty]
    private int _connectionTimeout = 30;

    [ObservableProperty]
    private string _defaultNamespace = "default";

    [ObservableProperty]
    private bool _skipTlsVerification;

    [ObservableProperty]
    private bool _hasValidKubeConfig;

    [ObservableProperty]
    private bool _isTestingConnection;

    [ObservableProperty]
    private string _testResult = "";

    [ObservableProperty]
    private bool _isAddingCluster;

    [ObservableProperty]
    private string _operationStatus = "";

    [ObservableProperty]
    private ObservableCollection<ContextInfo> _availableContexts = new();

    [ObservableProperty]
    private ObservableCollection<ClusterGroup> _availableFolders = new();

    public ICommand BrowseKubeConfigCommand { get; }
    public ICommand ValidateKubeConfigContentCommand { get; }
    public ICommand ClearKubeConfigContentCommand { get; }
    public ICommand TestConnectionCommand { get; }
    public ICommand AddClusterCommand { get; }
    public ICommand CancelCommand { get; }

    public bool CanTestConnection => HasValidKubeConfig && !IsTestingConnection && !IsAddingCluster;
    public bool CanAddCluster => !HasErrors && HasValidKubeConfig && !string.IsNullOrWhiteSpace(DisplayName) && !IsTestingConnection && !IsAddingCluster;

    public bool ShowStatus => !string.IsNullOrWhiteSpace(TestResult) || IsOperationInProgress;
    public bool IsOperationInProgress => IsTestingConnection || IsAddingCluster;
    public string TestResultColor => TestResult.StartsWith("✓") ? "Green" : TestResult.StartsWith("✗") ? "Red" : "Black";

    public event EventHandler? ClusterAdded;
    public event EventHandler? DialogCancelled;

    public AddClusterViewModel(
        IMultiClusterService multiClusterService,
        IKubernetesService kubernetesService,
        ILogService logService)
    {
        _multiClusterService = multiClusterService;
        _kubernetesService = kubernetesService;
        _logService = logService;

        BrowseKubeConfigCommand = new RelayCommand(BrowseKubeConfig);
        ValidateKubeConfigContentCommand = new AsyncRelayCommand(ValidateKubeConfigContentAsync);
        ClearKubeConfigContentCommand = new RelayCommand(ClearKubeConfigContent);
        TestConnectionCommand = new AsyncRelayCommand(TestConnectionAsync);
        AddClusterCommand = new AsyncRelayCommand(AddClusterAsync);
        CancelCommand = new RelayCommand(Cancel);

        // Set default kubeconfig path
        var defaultKubeConfig = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.UserProfile),
            ".kube", "config");
        
        if (File.Exists(defaultKubeConfig))
        {
            KubeConfigPath = defaultKubeConfig;
        }

        LoadAvailableFolders();
    }

    partial void OnKubeConfigPathChanged(string value)
    {
        ValidateKubeConfigPath();
    }

    partial void OnKubeConfigContentChanged(string value)
    {
        // Clear file path when content is provided
        if (!string.IsNullOrWhiteSpace(value))
        {
            KubeConfigPath = "";
        }
        ValidateAllProperties();
        OnPropertyChanged(nameof(CanAddCluster));
    }

    partial void OnDisplayNameChanged(string value)
    {
        ValidateAllProperties();
        OnPropertyChanged(nameof(CanAddCluster));
    }

    private void BrowseKubeConfig()
    {
        var openFileDialog = new OpenFileDialog
        {
            Title = "Select Kubeconfig File",
            Filter = "Config files (*.config;*.yaml;*.yml)|*.config;*.yaml;*.yml|All files (*.*)|*.*",
            CheckFileExists = true
        };

        if (!string.IsNullOrEmpty(KubeConfigPath))
        {
            openFileDialog.InitialDirectory = Path.GetDirectoryName(KubeConfigPath);
            openFileDialog.FileName = Path.GetFileName(KubeConfigPath);
        }

        if (openFileDialog.ShowDialog() == true)
        {
            KubeConfigPath = openFileDialog.FileName;
            // Clear content when file is selected
            KubeConfigContent = "";
        }
    }

    private async Task ValidateKubeConfigContentAsync()
    {
        if (string.IsNullOrWhiteSpace(KubeConfigContent))
        {
            HasValidKubeConfig = false;
            AvailableContexts.Clear();
            return;
        }

        try
        {
            // Create a temporary file to validate the content
            var tempFile = Path.GetTempFileName();
            await File.WriteAllTextAsync(tempFile, KubeConfigContent);

            var contexts = await _kubernetesService.GetContextInfoAsync(tempFile);
            AvailableContexts.Clear();

            foreach (var context in contexts)
            {
                AvailableContexts.Add(context);
            }

            SelectedContext = AvailableContexts.FirstOrDefault();
            HasValidKubeConfig = AvailableContexts.Count > 0;

            // Clean up temp file
            File.Delete(tempFile);

            OnPropertyChanged(nameof(CanTestConnection));
            OnPropertyChanged(nameof(CanAddCluster));
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to validate kubeconfig content", ex);
            HasValidKubeConfig = false;
            AvailableContexts.Clear();
        }
    }

    private void ClearKubeConfigContent()
    {
        KubeConfigContent = "";
        HasValidKubeConfig = false;
        AvailableContexts.Clear();
        OnPropertyChanged(nameof(CanTestConnection));
        OnPropertyChanged(nameof(CanAddCluster));
    }

    private async void ValidateKubeConfigPath()
    {
        try
        {
            // Skip validation if content is provided instead
            if (!string.IsNullOrWhiteSpace(KubeConfigContent))
            {
                return;
            }

            if (string.IsNullOrWhiteSpace(KubeConfigPath) || !File.Exists(KubeConfigPath))
            {
                HasValidKubeConfig = false;
                AvailableContexts.Clear();
                return;
            }

            var contexts = await _kubernetesService.GetContextInfoAsync(KubeConfigPath);
            AvailableContexts.Clear();

            foreach (var context in contexts)
            {
                AvailableContexts.Add(context);
            }

            SelectedContext = AvailableContexts.FirstOrDefault();
            HasValidKubeConfig = AvailableContexts.Any();

            OnPropertyChanged(nameof(CanTestConnection));
            OnPropertyChanged(nameof(CanAddCluster));
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to validate kubeconfig", ex);
            HasValidKubeConfig = false;
            AvailableContexts.Clear();
        }
    }

    private async Task TestConnectionAsync()
    {
        if (!CanTestConnection) return;

        try
        {
            IsTestingConnection = true;
            OperationStatus = "Testing connection...";
            TestResult = "Testing connection...";

            // Notify UI of property changes
            OnPropertyChanged(nameof(CanTestConnection));
            OnPropertyChanged(nameof(CanAddCluster));
            OnPropertyChanged(nameof(ShowStatus));
            OnPropertyChanged(nameof(IsOperationInProgress));

            string configPath = await GetEffectiveKubeConfigPathAsync();
            var success = await _kubernetesService.ConnectAsync(
                configPath,
                SelectedContext?.Name);

            if (success)
            {
                TestResult = "✓ Connection successful! Cluster is reachable.";
                OperationStatus = "";
                await _kubernetesService.DisconnectAsync();
            }
            else
            {
                TestResult = "✗ Connection failed - Unable to reach cluster";
                OperationStatus = "";
            }
        }
        catch (Exception ex)
        {
            TestResult = $"✗ Connection failed: {ex.Message}";
            OperationStatus = "";
            _logService.LogError("Connection test failed", ex);
        }
        finally
        {
            IsTestingConnection = false;

            // Notify UI of property changes
            OnPropertyChanged(nameof(CanTestConnection));
            OnPropertyChanged(nameof(CanAddCluster));
            OnPropertyChanged(nameof(ShowStatus));
            OnPropertyChanged(nameof(IsOperationInProgress));
            OnPropertyChanged(nameof(TestResultColor));
            OnPropertyChanged(nameof(CanTestConnection));
        }
    }

    private async Task AddClusterAsync()
    {
        if (!CanAddCluster) return;

        try
        {
            IsAddingCluster = true;
            OperationStatus = "Adding cluster...";
            TestResult = "Adding cluster...";

            // Notify UI of property changes
            OnPropertyChanged(nameof(CanTestConnection));
            OnPropertyChanged(nameof(CanAddCluster));
            OnPropertyChanged(nameof(ShowStatus));
            OnPropertyChanged(nameof(IsOperationInProgress));

            ValidateAllProperties();
            if (HasErrors)
            {
                TestResult = "✗ Please fix validation errors before adding cluster";
                return;
            }

            OperationStatus = "Creating cluster connection...";
            string configPath = await GetEffectiveKubeConfigPathAsync();

            // Log context information for debugging
            _logService.LogInfo($"Adding cluster '{DisplayName}' with context: '{SelectedContext?.Name ?? "null"}'");
            _logService.LogInfo($"Available contexts: {string.Join(", ", AvailableContexts.Select(c => c.Name))}");

            var connection = await _multiClusterService.AddConnectionAsync(
                DisplayName,
                configPath,
                SelectedContext?.Name);

            OperationStatus = "Updating cluster properties...";
            // Update connection properties
            connection.Description = Description;
            connection.Tags = Tags;
            connection.AutoConnect = AutoConnect;

            // Handle folder assignment
            if (!string.IsNullOrWhiteSpace(FolderName))
            {
                OperationStatus = "Organizing cluster into folder...";
                var folder = SelectedFolder ?? await _multiClusterService.CreateGroupAsync(FolderName);
                await _multiClusterService.AddConnectionToGroupAsync(connection.Id, folder.Id);
            }

            OperationStatus = "Finalizing cluster setup...";
            await _multiClusterService.UpdateConnectionAsync(connection);

            // Auto-connect to the cluster if AutoConnect is enabled or if it's the first cluster
            if (AutoConnect)
            {
                OperationStatus = "Connecting to cluster...";
                TestResult = "Connecting to cluster...";

                var connectSuccess = await _multiClusterService.ConnectToClusterAsync(connection.Id);
                if (connectSuccess)
                {
                    TestResult = $"✓ Cluster '{DisplayName}' added and connected successfully!";
                    _logService.LogInfo($"Successfully connected to cluster: {DisplayName}");
                }
                else
                {
                    TestResult = $"✓ Cluster '{DisplayName}' added successfully, but connection failed";
                    _logService.LogWarning($"Cluster added but connection failed: {DisplayName}");
                }
            }
            else
            {
                TestResult = $"✓ Cluster '{DisplayName}' added successfully!";
            }

            OperationStatus = "";
            _logService.LogInfo($"Successfully added cluster: {DisplayName}");

            // Wait a moment to show success message, then close dialog
            await Task.Delay(1500);
            ClusterAdded?.Invoke(this, EventArgs.Empty);
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to add cluster: {DisplayName}", ex);
            TestResult = $"✗ Failed to add cluster: {ex.Message}";
            OperationStatus = "";
        }
        finally
        {
            IsAddingCluster = false;

            // Notify UI of property changes
            OnPropertyChanged(nameof(CanTestConnection));
            OnPropertyChanged(nameof(CanAddCluster));
            OnPropertyChanged(nameof(ShowStatus));
            OnPropertyChanged(nameof(IsOperationInProgress));
            OnPropertyChanged(nameof(TestResultColor));
        }
    }

    private void Cancel()
    {
        DialogCancelled?.Invoke(this, EventArgs.Empty);
    }

    private async void LoadAvailableFolders()
    {
        try
        {
            var groups = await _multiClusterService.GetGroupsAsync();
            AvailableFolders.Clear();
            
            foreach (var group in groups)
            {
                AvailableFolders.Add(group);
            }
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to load available folders", ex);
        }
    }

    private async Task<string> GetEffectiveKubeConfigPathAsync()
    {
        // If content is provided, create a temporary file
        if (!string.IsNullOrWhiteSpace(KubeConfigContent))
        {
            var tempFile = Path.GetTempFileName();
            await File.WriteAllTextAsync(tempFile, KubeConfigContent);
            return tempFile;
        }

        // Otherwise use the file path
        return KubeConfigPath;
    }
}
