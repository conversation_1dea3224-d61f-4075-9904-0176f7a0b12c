using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DesK11.Core.Interfaces;
using DesK11.Models;
using Microsoft.Win32;

namespace DesK11.UI.ViewModels;

public partial class AddClusterViewModel : ObservableValidator
{
    private readonly IMultiClusterService _multiClusterService;
    private readonly IKubernetesService _kubernetesService;
    private readonly ILogService _logService;

    [ObservableProperty]
    [Required(ErrorMessage = "Display name is required")]
    [MinLength(1, ErrorMessage = "Display name cannot be empty")]
    private string _displayName = "";

    [ObservableProperty]
    private string _description = "";

    [ObservableProperty]
    [Required(ErrorMessage = "Kubeconfig path is required")]
    private string _kubeConfigPath = "";

    [ObservableProperty]
    private ContextInfo? _selectedContext;

    [ObservableProperty]
    private ClusterGroup? _selectedFolder;

    [ObservableProperty]
    private string _folderName = "";

    [ObservableProperty]
    private string _tags = "";

    [ObservableProperty]
    private bool _autoConnect;

    [ObservableProperty]
    private int _connectionTimeout = 30;

    [ObservableProperty]
    private string _defaultNamespace = "default";

    [ObservableProperty]
    private bool _skipTlsVerification;

    [ObservableProperty]
    private bool _hasValidKubeConfig;

    [ObservableProperty]
    private bool _isTestingConnection;

    [ObservableProperty]
    private string _testResult = "";

    [ObservableProperty]
    private ObservableCollection<ContextInfo> _availableContexts = new();

    [ObservableProperty]
    private ObservableCollection<ClusterGroup> _availableFolders = new();

    public ICommand BrowseKubeConfigCommand { get; }
    public ICommand TestConnectionCommand { get; }
    public ICommand AddClusterCommand { get; }
    public ICommand CancelCommand { get; }

    public bool CanTestConnection => HasValidKubeConfig && !IsTestingConnection;
    public bool CanAddCluster => !HasErrors && HasValidKubeConfig && !string.IsNullOrWhiteSpace(DisplayName);

    public event EventHandler? ClusterAdded;
    public event EventHandler? DialogCancelled;

    public AddClusterViewModel(
        IMultiClusterService multiClusterService,
        IKubernetesService kubernetesService,
        ILogService logService)
    {
        _multiClusterService = multiClusterService;
        _kubernetesService = kubernetesService;
        _logService = logService;

        BrowseKubeConfigCommand = new RelayCommand(BrowseKubeConfig);
        TestConnectionCommand = new AsyncRelayCommand(TestConnectionAsync);
        AddClusterCommand = new AsyncRelayCommand(AddClusterAsync);
        CancelCommand = new RelayCommand(Cancel);

        // Set default kubeconfig path
        var defaultKubeConfig = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.UserProfile),
            ".kube", "config");
        
        if (File.Exists(defaultKubeConfig))
        {
            KubeConfigPath = defaultKubeConfig;
        }

        LoadAvailableFolders();
    }

    partial void OnKubeConfigPathChanged(string value)
    {
        ValidateKubeConfigPath();
    }

    partial void OnDisplayNameChanged(string value)
    {
        ValidateAllProperties();
        OnPropertyChanged(nameof(CanAddCluster));
    }

    private void BrowseKubeConfig()
    {
        var openFileDialog = new OpenFileDialog
        {
            Title = "Select Kubeconfig File",
            Filter = "Config files (*.config;*.yaml;*.yml)|*.config;*.yaml;*.yml|All files (*.*)|*.*",
            CheckFileExists = true
        };

        if (!string.IsNullOrEmpty(KubeConfigPath))
        {
            openFileDialog.InitialDirectory = Path.GetDirectoryName(KubeConfigPath);
            openFileDialog.FileName = Path.GetFileName(KubeConfigPath);
        }

        if (openFileDialog.ShowDialog() == true)
        {
            KubeConfigPath = openFileDialog.FileName;
        }
    }

    private async void ValidateKubeConfigPath()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(KubeConfigPath) || !File.Exists(KubeConfigPath))
            {
                HasValidKubeConfig = false;
                AvailableContexts.Clear();
                return;
            }

            var contexts = await _kubernetesService.GetContextInfoAsync(KubeConfigPath);
            AvailableContexts.Clear();
            
            foreach (var context in contexts)
            {
                AvailableContexts.Add(context);
            }

            SelectedContext = AvailableContexts.FirstOrDefault();
            HasValidKubeConfig = AvailableContexts.Any();
            
            OnPropertyChanged(nameof(CanTestConnection));
            OnPropertyChanged(nameof(CanAddCluster));
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to validate kubeconfig", ex);
            HasValidKubeConfig = false;
            AvailableContexts.Clear();
        }
    }

    private async Task TestConnectionAsync()
    {
        if (!CanTestConnection) return;

        try
        {
            IsTestingConnection = true;
            TestResult = "Testing connection...";

            var success = await _kubernetesService.ConnectAsync(
                KubeConfigPath, 
                SelectedContext?.Name);

            if (success)
            {
                TestResult = "✓ Connection successful!";
                await _kubernetesService.DisconnectAsync();
            }
            else
            {
                TestResult = "✗ Connection failed";
            }
        }
        catch (Exception ex)
        {
            TestResult = $"✗ Connection failed: {ex.Message}";
            _logService.LogError("Connection test failed", ex);
        }
        finally
        {
            IsTestingConnection = false;
            OnPropertyChanged(nameof(CanTestConnection));
        }
    }

    private async Task AddClusterAsync()
    {
        if (!CanAddCluster) return;

        try
        {
            ValidateAllProperties();
            if (HasErrors) return;

            var connection = await _multiClusterService.AddConnectionAsync(
                DisplayName,
                KubeConfigPath,
                SelectedContext?.Name);

            // Update connection properties
            connection.Description = Description;
            connection.Tags = Tags;
            connection.AutoConnect = AutoConnect;

            // Handle folder assignment
            if (!string.IsNullOrWhiteSpace(FolderName))
            {
                var folder = SelectedFolder ?? await _multiClusterService.CreateGroupAsync(FolderName);
                await _multiClusterService.AddConnectionToGroupAsync(connection.Id, folder.Id);
            }

            await _multiClusterService.UpdateConnectionAsync(connection);

            _logService.LogInfo($"Successfully added cluster: {DisplayName}");
            ClusterAdded?.Invoke(this, EventArgs.Empty);
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to add cluster: {DisplayName}", ex);
            TestResult = $"✗ Failed to add cluster: {ex.Message}";
        }
    }

    private void Cancel()
    {
        DialogCancelled?.Invoke(this, EventArgs.Empty);
    }

    private async void LoadAvailableFolders()
    {
        try
        {
            var groups = await _multiClusterService.GetGroupsAsync();
            AvailableFolders.Clear();
            
            foreach (var group in groups)
            {
                AvailableFolders.Add(group);
            }
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to load available folders", ex);
        }
    }
}
