using System.Collections.ObjectModel;
using System.Text.Json;
using DesK11.Core.Interfaces;
using DesK11.Models;

namespace DesK11.Core.Services;

public class MultiClusterService : IMultiClusterService
{
    private readonly IKubernetesService _kubernetesService;
    private readonly IConfigurationService _configurationService;
    private readonly ILogService _logService;
    private readonly ObservableCollection<ClusterConnection> _connections = new();
    private readonly ObservableCollection<ClusterGroup> _groups = new();
    private ClusterConnection? _activeConnection;
    private MultiClusterSession _currentSession = new();

    public ClusterConnection? ActiveConnection => _activeConnection;
    public IEnumerable<ClusterConnection> ConnectedClusters => 
        _connections.Where(c => c.Status == ClusterConnectionStatus.Connected);

    public event EventHandler<ClusterConnection>? ConnectionAdded;
    public event EventHandler<ClusterConnection>? ConnectionRemoved;
    public event EventHandler<ClusterConnection>? ConnectionStatusChanged;
    public event EventHandler<ClusterConnection>? ActiveConnectionChanged;

    public MultiClusterService(
        IKubernetesService kubernetesService,
        IConfigurationService configurationService,
        ILogService logService)
    {
        _kubernetesService = kubernetesService;
        _configurationService = configurationService;
        _logService = logService;

        // Subscribe to Kubernetes service events
        _kubernetesService.ClusterConnected += OnClusterConnected;
        _kubernetesService.ClusterDisconnected += OnClusterDisconnected;
        _kubernetesService.ErrorOccurred += OnKubernetesError;

        // Initialize session
        _currentSession.SessionName = $"Session_{DateTime.Now:yyyyMMdd_HHmmss}";
        _currentSession.Connections = _connections;
        _currentSession.Groups = _groups;
    }

    public async Task<IEnumerable<ClusterConnection>> GetConnectionsAsync()
    {
        if (!_connections.Any())
        {
            await LoadConnectionsFromConfigAsync();
        }
        return _connections;
    }

    public async Task<ClusterConnection> AddConnectionAsync(string displayName, string kubeConfigPath, string? contextName = null)
    {
        try
        {
            // Validate the kubeconfig and context
            var contexts = await _kubernetesService.GetContextInfoAsync(kubeConfigPath);
            var targetContext = contextName != null
                ? contexts.FirstOrDefault(c => c.Name == contextName)
                : contexts.FirstOrDefault();

            if (targetContext == null)
            {
                throw new InvalidOperationException($"Context '{contextName ?? "default"}' not found in kubeconfig");
            }

            var connection = new ClusterConnection
            {
                DisplayName = displayName,
                KubeConfigPath = kubeConfigPath,
                ContextName = targetContext.Name,
                Status = ClusterConnectionStatus.Disconnected,
                CreatedAt = DateTime.Now
            };

            _connections.Add(connection);
            await SaveConnectionsToConfigAsync();

            _logService.LogInfo($"Added cluster connection: {displayName}");
            ConnectionAdded?.Invoke(this, connection);

            return connection;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to add cluster connection: {displayName}", ex);
            throw;
        }
    }

    public async Task<bool> RemoveConnectionAsync(string connectionId)
    {
        try
        {
            var connection = _connections.FirstOrDefault(c => c.Id == connectionId);
            if (connection == null)
                return false;

            // Disconnect if currently connected
            if (connection.Status == ClusterConnectionStatus.Connected)
            {
                await DisconnectFromClusterAsync(connectionId);
            }

            // Remove from groups
            foreach (var group in _groups)
            {
                group.Clusters.Remove(connection);
            }

            _connections.Remove(connection);
            await SaveConnectionsToConfigAsync();

            _logService.LogInfo($"Removed cluster connection: {connection.DisplayName}");
            ConnectionRemoved?.Invoke(this, connection);

            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to remove cluster connection: {connectionId}", ex);
            return false;
        }
    }

    public async Task<bool> UpdateConnectionAsync(ClusterConnection connection)
    {
        try
        {
            var existingConnection = _connections.FirstOrDefault(c => c.Id == connection.Id);
            if (existingConnection == null)
                return false;

            // Update properties
            existingConnection.DisplayName = connection.DisplayName;
            existingConnection.KubeConfigPath = connection.KubeConfigPath;
            existingConnection.ContextName = connection.ContextName;
            existingConnection.AutoConnect = connection.AutoConnect;
            existingConnection.Tags = connection.Tags;
            existingConnection.Description = connection.Description;

            await SaveConnectionsToConfigAsync();
            _logService.LogInfo($"Updated cluster connection: {connection.DisplayName}");

            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to update cluster connection: {connection.Id}", ex);
            return false;
        }
    }

    public async Task<bool> ConnectToClusterAsync(string connectionId)
    {
        try
        {
            var connection = _connections.FirstOrDefault(c => c.Id == connectionId);
            if (connection == null)
                return false;

            connection.Status = ClusterConnectionStatus.Connecting;
            ConnectionStatusChanged?.Invoke(this, connection);

            var success = await _kubernetesService.ConnectAsync(connection.KubeConfigPath, connection.ContextName);
            
            if (success)
            {
                connection.Status = ClusterConnectionStatus.Connected;
                connection.LastUsed = DateTime.Now;
                connection.ClusterInfo = await _kubernetesService.GetClusterInfoAsync();
                
                // Set as active if no active connection
                if (_activeConnection == null)
                {
                    _activeConnection = connection;
                    _activeConnection.IsActive = true;
                    ActiveConnectionChanged?.Invoke(this, _activeConnection);
                }
            }
            else
            {
                connection.Status = ClusterConnectionStatus.Error;
            }

            ConnectionStatusChanged?.Invoke(this, connection);
            await SaveConnectionsToConfigAsync();

            return success;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to connect to cluster: {connectionId}", ex);
            
            var connection = _connections.FirstOrDefault(c => c.Id == connectionId);
            if (connection != null)
            {
                connection.Status = ClusterConnectionStatus.Error;
                ConnectionStatusChanged?.Invoke(this, connection);
            }
            
            return false;
        }
    }

    public async Task<bool> DisconnectFromClusterAsync(string connectionId)
    {
        try
        {
            var connection = _connections.FirstOrDefault(c => c.Id == connectionId);
            if (connection == null)
                return false;

            // If this is the active connection, disconnect the Kubernetes service
            if (connection == _activeConnection)
            {
                await _kubernetesService.DisconnectAsync();
                _activeConnection.IsActive = false;
                _activeConnection = null;
                ActiveConnectionChanged?.Invoke(this, null!);
            }

            connection.Status = ClusterConnectionStatus.Disconnected;
            connection.ClusterInfo = null;
            
            ConnectionStatusChanged?.Invoke(this, connection);
            await SaveConnectionsToConfigAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to disconnect from cluster: {connectionId}", ex);
            return false;
        }
    }

    public async Task<bool> SwitchActiveClusterAsync(string connectionId)
    {
        try
        {
            var newConnection = _connections.FirstOrDefault(c => c.Id == connectionId);
            if (newConnection == null || newConnection.Status != ClusterConnectionStatus.Connected)
                return false;

            // Deactivate current connection
            if (_activeConnection != null)
            {
                _activeConnection.IsActive = false;
            }

            // Switch context in Kubernetes service
            var success = await _kubernetesService.SwitchContextAsync(newConnection.ContextName);
            if (!success)
                return false;

            // Set new active connection
            _activeConnection = newConnection;
            _activeConnection.IsActive = true;
            _activeConnection.LastUsed = DateTime.Now;

            ActiveConnectionChanged?.Invoke(this, _activeConnection);
            await SaveConnectionsToConfigAsync();

            _logService.LogInfo($"Switched to cluster: {_activeConnection.DisplayName}");
            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to switch to cluster: {connectionId}", ex);
            return false;
        }
    }

    public async Task<MultiClusterSession> GetCurrentSessionAsync()
    {
        return await Task.FromResult(_currentSession);
    }

    public async Task SaveSessionAsync(MultiClusterSession session)
    {
        try
        {
            _currentSession = session;
            await SaveConnectionsToConfigAsync();
            _logService.LogInfo($"Saved session: {session.SessionName}");
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to save session", ex);
            throw;
        }
    }

    public async Task<IEnumerable<ClusterGroup>> GetGroupsAsync()
    {
        return await Task.FromResult(_groups);
    }

    public async Task<ClusterGroup> CreateGroupAsync(string name, string description = "")
    {
        var group = new ClusterGroup
        {
            Name = name,
            Description = description
        };

        _groups.Add(group);
        await SaveConnectionsToConfigAsync();

        _logService.LogInfo($"Created cluster group: {name}");
        return group;
    }

    public async Task<bool> UpdateGroupAsync(ClusterGroup group)
    {
        try
        {
            var existingGroup = _groups.FirstOrDefault(g => g.Id == group.Id);
            if (existingGroup == null)
                return false;

            existingGroup.Name = group.Name;
            existingGroup.Description = group.Description;
            existingGroup.Color = group.Color;

            await SaveConnectionsToConfigAsync();
            _logService.LogInfo($"Updated cluster group: {group.Name}");
            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to update group: {group.Name}", ex);
            return false;
        }
    }

    public async Task<bool> DeleteGroupAsync(string groupId)
    {
        try
        {
            var group = _groups.FirstOrDefault(g => g.Id == groupId);
            if (group == null)
                return false;

            // Move clusters from this group to ungrouped
            foreach (var cluster in group.Clusters.ToList())
            {
                group.Clusters.Remove(cluster);
            }

            _groups.Remove(group);
            await SaveConnectionsToConfigAsync();

            _logService.LogInfo($"Deleted cluster group: {group.Name}");
            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to delete group: {groupId}", ex);
            return false;
        }
    }

    public async Task<bool> AddConnectionToGroupAsync(string connectionId, string groupId)
    {
        try
        {
            var group = _groups.FirstOrDefault(g => g.Id == groupId);
            var connection = _connections.FirstOrDefault(c => c.Id == connectionId);

            if (group == null || connection == null)
                return false;

            if (!group.Clusters.Contains(connection))
            {
                group.Clusters.Add(connection);
                await SaveConnectionsToConfigAsync();
            }

            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to add connection to group: {groupId}", ex);
            return false;
        }
    }

    public async Task<bool> RemoveConnectionFromGroupAsync(string connectionId, string groupId)
    {
        try
        {
            var group = _groups.FirstOrDefault(g => g.Id == groupId);
            var connection = _connections.FirstOrDefault(c => c.Id == connectionId);

            if (group == null || connection == null)
                return false;

            group.Clusters.Remove(connection);
            await SaveConnectionsToConfigAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to remove connection from group: {groupId}", ex);
            return false;
        }
    }

    public async Task<IEnumerable<ClusterHealthStatus>> GetAllClusterHealthAsync()
    {
        var healthStatuses = new List<ClusterHealthStatus>();

        foreach (var connection in _connections.Where(c => c.Status == ClusterConnectionStatus.Connected))
        {
            try
            {
                // Switch to this cluster temporarily to get health
                var currentActive = _activeConnection;
                await SwitchActiveClusterAsync(connection.Id);

                var health = await _kubernetesService.GetClusterHealthAsync();
                healthStatuses.Add(health);

                // Switch back to original active cluster
                if (currentActive != null && currentActive != connection)
                {
                    await SwitchActiveClusterAsync(currentActive.Id);
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"Failed to get health for cluster: {connection.DisplayName}", ex);
                healthStatuses.Add(new ClusterHealthStatus
                {
                    ClusterId = connection.Id,
                    IsHealthy = false,
                    LastCheck = DateTime.Now,
                    Issues = new List<string> { ex.Message }
                });
            }
        }

        return healthStatuses;
    }

    public async Task<IEnumerable<ClusterComparisonResult>> CompareClusterResourcesAsync(
        IEnumerable<string> connectionIds, ResourceType resourceType)
    {
        var results = new List<ClusterComparisonResult>();
        var clusterData = new Dictionary<string, Dictionary<string, object>>();

        // Collect data from each cluster
        foreach (var connectionId in connectionIds)
        {
            var connection = _connections.FirstOrDefault(c => c.Id == connectionId);
            if (connection?.Status != ClusterConnectionStatus.Connected)
                continue;

            try
            {
                await SwitchActiveClusterAsync(connectionId);
                var data = await GetResourceDataForComparison(resourceType);
                clusterData[connection.DisplayName] = data;
            }
            catch (Exception ex)
            {
                _logService.LogError($"Failed to get data from cluster: {connection.DisplayName}", ex);
            }
        }

        // Compare the data
        if (clusterData.Count > 1)
        {
            var allKeys = clusterData.Values.SelectMany(d => d.Keys).Distinct();

            foreach (var key in allKeys)
            {
                var values = new Dictionary<string, object>();
                var hasDifferences = false;
                object? firstValue = null;

                foreach (var (clusterName, data) in clusterData)
                {
                    var value = data.GetValueOrDefault(key, "N/A");
                    values[clusterName] = value;

                    if (firstValue == null)
                        firstValue = value;
                    else if (!Equals(firstValue, value))
                        hasDifferences = true;
                }

                results.Add(new ClusterComparisonResult
                {
                    Property = key,
                    Values = values,
                    HasDifferences = hasDifferences
                });
            }
        }

        return results;
    }

    private async Task<Dictionary<string, object>> GetResourceDataForComparison(ResourceType resourceType)
    {
        var data = new Dictionary<string, object>();

        switch (resourceType)
        {
            case ResourceType.Pod:
                var pods = await _kubernetesService.GetPodsAsync();
                data["TotalPods"] = pods.Count();
                data["RunningPods"] = pods.Count(p => p.Status == "Running");
                data["PendingPods"] = pods.Count(p => p.Status == "Pending");
                data["FailedPods"] = pods.Count(p => p.Status == "Failed");
                break;

            case ResourceType.Node:
                var nodes = await _kubernetesService.GetNodesAsync();
                data["TotalNodes"] = nodes.Count();
                data["ReadyNodes"] = nodes.Count(n => n.Status == "Ready");
                break;

            case ResourceType.Service:
                var services = await _kubernetesService.GetServicesAsync();
                data["TotalServices"] = services.Count();
                data["ClusterIPServices"] = services.Count(s => s.Type == "ClusterIP");
                data["LoadBalancerServices"] = services.Count(s => s.Type == "LoadBalancer");
                break;

            case ResourceType.Namespace:
                var namespaces = await _kubernetesService.GetNamespacesAsync();
                data["TotalNamespaces"] = namespaces.Count();
                break;
        }

        return data;
    }

    private async Task LoadConnectionsFromConfigAsync()
    {
        try
        {
            var settings = await _configurationService.GetSettingsAsync();
            if (settings.TryGetValue("ClusterConnections", out var connectionsJson) && connectionsJson is string json)
            {
                var connections = JsonSerializer.Deserialize<List<ClusterConnection>>(json);
                if (connections != null)
                {
                    _connections.Clear();
                    foreach (var connection in connections)
                    {
                        _connections.Add(connection);
                    }
                }
            }

            if (settings.TryGetValue("ClusterGroups", out var groupsJson) && groupsJson is string groupJson)
            {
                var groups = JsonSerializer.Deserialize<List<ClusterGroup>>(groupJson);
                if (groups != null)
                {
                    _groups.Clear();
                    foreach (var group in groups)
                    {
                        _groups.Add(group);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to load connections from config", ex);
        }
    }

    private async Task SaveConnectionsToConfigAsync()
    {
        try
        {
            var connectionsJson = JsonSerializer.Serialize(_connections);
            await _configurationService.SaveSettingAsync("ClusterConnections", connectionsJson);

            var groupsJson = JsonSerializer.Serialize(_groups);
            await _configurationService.SaveSettingAsync("ClusterGroups", groupsJson);
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to save connections to config", ex);
        }
    }

    private void OnClusterConnected(object? sender, ClusterInfo clusterInfo)
    {
        var connection = _connections.FirstOrDefault(c => c.ContextName == clusterInfo.ContextName);
        if (connection != null)
        {
            connection.Status = ClusterConnectionStatus.Connected;
            connection.ClusterInfo = clusterInfo;
            connection.LastUsed = DateTime.Now;
            ConnectionStatusChanged?.Invoke(this, connection);
        }
    }

    private void OnClusterDisconnected(object? sender, EventArgs e)
    {
        if (_activeConnection != null)
        {
            _activeConnection.Status = ClusterConnectionStatus.Disconnected;
            _activeConnection.ClusterInfo = null;
            _activeConnection.IsActive = false;
            ConnectionStatusChanged?.Invoke(this, _activeConnection);
            _activeConnection = null;
            ActiveConnectionChanged?.Invoke(this, null!);
        }
    }

    private void OnKubernetesError(object? sender, string error)
    {
        if (_activeConnection != null)
        {
            _activeConnection.Status = ClusterConnectionStatus.Error;
            ConnectionStatusChanged?.Invoke(this, _activeConnection);
        }
    }
}
