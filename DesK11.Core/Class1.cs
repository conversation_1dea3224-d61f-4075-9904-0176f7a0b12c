using DesK11.Models;

namespace DesK11.Core.Interfaces;

public interface IKubernetesService
{
    Task<bool> ConnectAsync(string kubeConfigPath = "", string? contextName = null);
    Task DisconnectAsync();
    Task<ClusterInfo?> GetClusterInfoAsync();
    Task<IEnumerable<PodInfo>> GetPodsAsync(string? namespaceName = null);
    Task<IEnumerable<ServiceInfo>> GetServicesAsync(string? namespaceName = null);
    Task<IEnumerable<NodeInfo>> GetNodesAsync();
    Task<IEnumerable<string>> GetNamespacesAsync();
    Task<string> GetPodLogsAsync(string podName, string namespaceName, int tailLines = 100);
    Task<bool> DeletePodAsync(string podName, string namespaceName);
    Task<bool> RestartPodAsync(string podName, string namespaceName);
    Task<bool> ScaleDeploymentAsync(string deploymentName, string namespaceName, int replicas);
    Task<IEnumerable<KubernetesContext>> GetAvailableContextsAsync(string kubeConfigPath = "");
    Task<bool> SwitchContextAsync(string contextName);
    Task<ClusterHealthStatus> GetClusterHealthAsync();

    ClusterInfo? CurrentCluster { get; }
    string? CurrentContext { get; }
    bool IsConnected { get; }

    event EventHandler<ClusterInfo>? ClusterConnected;
    event EventHandler? ClusterDisconnected;
    event EventHandler<string>? ErrorOccurred;
    event EventHandler<string>? ContextChanged;
}

public interface IMultiClusterService
{
    Task<IEnumerable<ClusterConnection>> GetConnectionsAsync();
    Task<ClusterConnection> AddConnectionAsync(string displayName, string kubeConfigPath, string? contextName = null);
    Task<bool> RemoveConnectionAsync(string connectionId);
    Task<bool> UpdateConnectionAsync(ClusterConnection connection);
    Task<bool> ConnectToClusterAsync(string connectionId);
    Task<bool> DisconnectFromClusterAsync(string connectionId);
    Task<bool> SwitchActiveClusterAsync(string connectionId);
    Task<MultiClusterSession> GetCurrentSessionAsync();
    Task SaveSessionAsync(MultiClusterSession session);
    Task<IEnumerable<ClusterGroup>> GetGroupsAsync();
    Task<ClusterGroup> CreateGroupAsync(string name, string description = "");
    Task<bool> AddConnectionToGroupAsync(string groupId, string connectionId);
    Task<bool> RemoveConnectionFromGroupAsync(string groupId, string connectionId);
    Task<IEnumerable<ClusterHealthStatus>> GetAllClusterHealthAsync();
    Task<IEnumerable<ClusterComparisonResult>> CompareClusterResourcesAsync(IEnumerable<string> connectionIds, ResourceType resourceType);

    ClusterConnection? ActiveConnection { get; }
    IEnumerable<ClusterConnection> ConnectedClusters { get; }

    event EventHandler<ClusterConnection>? ConnectionAdded;
    event EventHandler<ClusterConnection>? ConnectionRemoved;
    event EventHandler<ClusterConnection>? ConnectionStatusChanged;
    event EventHandler<ClusterConnection>? ActiveConnectionChanged;
}

public interface IConfigurationService
{
    Task<Dictionary<string, object>> GetSettingsAsync();
    Task<T?> GetSettingAsync<T>(string key, T? defaultValue = default);
    Task SaveSettingAsync<T>(string key, T value);
    Task RemoveSettingAsync(string key);
    Task ClearSettingsAsync();
}

public interface ILogService
{
    void LogInfo(string message);
    void LogWarning(string message);
    void LogError(string message, Exception? exception = null);
    void LogDebug(string message);
    IEnumerable<LogEntry> GetLogs(LogLevel? level = null, int maxCount = 1000);
    void ClearLogs();

    event EventHandler<LogEntry>? LogAdded;
}

public enum LogLevel
{
    Debug,
    Info,
    Warning,
    Error
}

public interface IConfigurationService
{
    Task<string> GetKubeConfigPathAsync();
    Task SaveKubeConfigPathAsync(string path);
    Task<Dictionary<string, object>> GetSettingsAsync();
    Task SaveSettingAsync(string key, object value);
}

public interface ILogService
{
    void LogInfo(string message);
    void LogWarning(string message);
    void LogError(string message, Exception? exception = null);
    void LogDebug(string message);
}
