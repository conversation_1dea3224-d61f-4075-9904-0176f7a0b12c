using DesK11.Core.Interfaces;
using DesK11.Models;

namespace DesK11.Core.Services;

public class LogService : ILogService
{
    private readonly List<LogEntry> _logs = new();
    private readonly object _lockObject = new();

    public event EventHandler<LogEntry>? LogAdded;

    public void LogInfo(string message)
    {
        AddLog(LogLevel.Info, message);
    }

    public void LogWarning(string message)
    {
        AddLog(LogLevel.Warning, message);
    }

    public void LogError(string message, Exception? exception = null)
    {
        var fullMessage = exception != null ? $"{message}: {exception.Message}" : message;
        AddLog(LogLevel.Error, fullMessage);
    }

    public void LogDebug(string message)
    {
        AddLog(LogLevel.Debug, message);
    }

    public IEnumerable<LogEntry> GetLogs(LogLevel? level = null, int maxCount = 1000)
    {
        lock (_lockObject)
        {
            var query = _logs.AsEnumerable();
            
            if (level.HasValue)
            {
                query = query.Where(l => l.Level == level.Value);
            }

            return query.OrderByDescending(l => l.Timestamp)
                       .Take(maxCount)
                       .ToList();
        }
    }

    public void ClearLogs()
    {
        lock (_lockObject)
        {
            _logs.Clear();
        }
    }

    private void AddLog(LogLevel level, string message)
    {
        var logEntry = new LogEntry
        {
            Level = level,
            Message = message,
            Timestamp = DateTime.Now
        };

        lock (_lockObject)
        {
            _logs.Add(logEntry);
            
            // Keep only the last 10000 entries to prevent memory issues
            if (_logs.Count > 10000)
            {
                _logs.RemoveRange(0, _logs.Count - 10000);
            }
        }

        LogAdded?.Invoke(this, logEntry);
    }
}


