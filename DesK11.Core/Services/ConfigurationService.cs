using System.Text.Json;
using DesK11.Core.Interfaces;

namespace DesK11.Core.Services;

public class ConfigurationService : IConfigurationService
{
    private readonly string _configFilePath;
    private readonly Dictionary<string, object> _settings = new();
    private readonly object _lockObject = new();

    public ConfigurationService()
    {
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        var appFolder = Path.Combine(appDataPath, "DesK11");
        
        if (!Directory.Exists(appFolder))
        {
            Directory.CreateDirectory(appFolder);
        }
        
        _configFilePath = Path.Combine(appFolder, "config.json");
        
        // Load existing settings
        _ = LoadSettingsAsync();
    }

    public async Task<Dictionary<string, object>> GetSettingsAsync()
    {
        await LoadSettingsAsync();
        
        lock (_lockObject)
        {
            return new Dictionary<string, object>(_settings);
        }
    }

    public async Task<T?> GetSettingAsync<T>(string key, T? defaultValue = default)
    {
        await LoadSettingsAsync();
        
        lock (_lockObject)
        {
            if (_settings.TryGetValue(key, out var value))
            {
                if (value is JsonElement jsonElement)
                {
                    try
                    {
                        return JsonSerializer.Deserialize<T>(jsonElement.GetRawText());
                    }
                    catch
                    {
                        return defaultValue;
                    }
                }
                
                if (value is T directValue)
                {
                    return directValue;
                }
                
                try
                {
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch
                {
                    return defaultValue;
                }
            }
            
            return defaultValue;
        }
    }

    public async Task SaveSettingAsync<T>(string key, T value)
    {
        lock (_lockObject)
        {
            _settings[key] = value!;
        }
        
        await SaveSettingsAsync();
    }

    public async Task RemoveSettingAsync(string key)
    {
        lock (_lockObject)
        {
            _settings.Remove(key);
        }
        
        await SaveSettingsAsync();
    }

    public async Task ClearSettingsAsync()
    {
        lock (_lockObject)
        {
            _settings.Clear();
        }
        
        await SaveSettingsAsync();
    }

    private async Task LoadSettingsAsync()
    {
        try
        {
            if (!File.Exists(_configFilePath))
            {
                return;
            }

            var json = await File.ReadAllTextAsync(_configFilePath);
            if (string.IsNullOrWhiteSpace(json))
            {
                return;
            }

            var loadedSettings = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(json);
            if (loadedSettings != null)
            {
                lock (_lockObject)
                {
                    _settings.Clear();
                    foreach (var kvp in loadedSettings)
                    {
                        _settings[kvp.Key] = kvp.Value;
                    }
                }
            }
        }
        catch (Exception)
        {
            // If loading fails, continue with empty settings
            // In a production app, you might want to log this error
        }
    }

    private async Task SaveSettingsAsync()
    {
        try
        {
            Dictionary<string, object> settingsToSave;
            lock (_lockObject)
            {
                settingsToSave = new Dictionary<string, object>(_settings);
            }

            var json = JsonSerializer.Serialize(settingsToSave, new JsonSerializerOptions
            {
                WriteIndented = true
            });

            await File.WriteAllTextAsync(_configFilePath, json);
        }
        catch (Exception)
        {
            // If saving fails, continue silently
            // In a production app, you might want to log this error
        }
    }
}
