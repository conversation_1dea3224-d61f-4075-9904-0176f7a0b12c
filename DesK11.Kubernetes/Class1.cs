using k8s;
using k8s.Models;
using DesK11.Core.Interfaces;
using DesK11.Models;

namespace DesK11.Kubernetes;

public class KubernetesService : IKubernetesService
{
    private IKubernetes? _client;
    private ClusterInfo? _currentCluster;
    private KubernetesClientConfiguration? _currentConfig;
    private readonly ILogService _logService;

    public ClusterInfo? CurrentCluster => _currentCluster;
    public string? CurrentContext => _currentConfig?.CurrentContext;
    public bool IsConnected => _client != null && _currentCluster?.IsConnected == true;

    public event EventHandler<ClusterInfo>? ClusterConnected;
    public event EventHandler? ClusterDisconnected;
    public event EventHandler<string>? ErrorOccurred;
    public event EventHandler<string>? ContextChanged;

    public KubernetesService(ILogService logService)
    {
        _logService = logService;
    }

    public async Task<bool> ConnectAsync(string kubeConfigPath = "", string? contextName = null)
    {
        try
        {
            KubernetesClientConfiguration config;

            if (string.IsNullOrEmpty(kubeConfigPath))
            {
                config = KubernetesClientConfiguration.BuildDefaultConfig();
            }
            else
            {
                config = KubernetesClientConfiguration.BuildConfigFromConfigFile(kubeConfigPath);
            }

            _client = new k8s.Kubernetes(config);
            _currentConfig = config;

            // Test connection by getting API resources
            await _client.CoreV1.GetAPIResourcesAsync();

            _currentCluster = new ClusterInfo
            {
                Name = config.CurrentContext ?? "default",
                Server = config.Host,
                Version = "v1.0.0", // Simplified for now
                ContextName = config.CurrentContext ?? "default",
                KubeConfigPath = kubeConfigPath,
                IsConnected = true,
                LastConnected = DateTime.Now
            };

            _logService.LogInfo($"Connected to cluster: {_currentCluster.Name}");
            ClusterConnected?.Invoke(this, _currentCluster);

            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to connect to Kubernetes cluster", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return false;
        }
    }

    public async Task<bool> ConnectAsync(string kubeConfigPath = "", string? contextName = null)
    {
        try
        {
            KubernetesClientConfiguration config;

            if (string.IsNullOrEmpty(kubeConfigPath))
            {
                config = KubernetesClientConfiguration.BuildDefaultConfig();
            }
            else
            {
                config = KubernetesClientConfiguration.BuildConfigFromConfigFile(kubeConfigPath);
            }

            // Switch to specific context if provided
            if (!string.IsNullOrEmpty(contextName))
            {
                config.CurrentContext = contextName;
            }

            _client = new k8s.Kubernetes(config);
            _currentConfig = config;

            // Test connection by getting cluster version
            var version = await _client.GetCodeAsync();

            _currentCluster = new ClusterInfo
            {
                Name = config.CurrentContext ?? "default",
                Server = config.Host,
                Version = version.GitVersion ?? "Unknown",
                ContextName = config.CurrentContext ?? "default",
                KubeConfigPath = kubeConfigPath,
                IsConnected = true
            };

            _logService.LogInfo($"Connected to cluster: {_currentCluster.Name} (Context: {_currentCluster.ContextName})");
            ClusterConnected?.Invoke(this, _currentCluster);

            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to connect to Kubernetes cluster", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return false;
        }
    }

    public async Task<IEnumerable<KubernetesContext>> GetAvailableContextsAsync(string kubeConfigPath = "")
    {
        try
        {
            KubernetesClientConfiguration config;

            if (string.IsNullOrEmpty(kubeConfigPath))
            {
                config = KubernetesClientConfiguration.BuildDefaultConfig();
            }
            else
            {
                config = KubernetesClientConfiguration.BuildConfigFromConfigFile(kubeConfigPath);
            }

            var contexts = new List<KubernetesContext>();

            // Get contexts from the configuration
            if (config.Contexts != null)
            {
                foreach (var context in config.Contexts)
                {
                    contexts.Add(new KubernetesContext
                    {
                        Name = context.Name,
                        Cluster = context.ContextDetails?.Cluster ?? "Unknown",
                        User = context.ContextDetails?.User ?? "Unknown",
                        Namespace = context.ContextDetails?.Namespace ?? "default",
                        IsActive = context.Name == config.CurrentContext
                    });
                }
            }

            return contexts;
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to get available contexts", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return Enumerable.Empty<KubernetesContext>();
        }
    }

    public async Task<bool> SwitchContextAsync(string contextName)
    {
        try
        {
            if (_currentConfig == null)
            {
                _logService.LogError("No current configuration available for context switch");
                return false;
            }

            // Update the current context
            _currentConfig.CurrentContext = contextName;

            // Recreate the client with new context
            _client?.Dispose();
            _client = new k8s.Kubernetes(_currentConfig);

            // Test the new connection
            var version = await _client.GetCodeAsync();

            // Update cluster info
            if (_currentCluster != null)
            {
                _currentCluster.ContextName = contextName;
                _currentCluster.Name = contextName;
                _currentCluster.Version = version.GitVersion ?? "Unknown";
            }

            _logService.LogInfo($"Switched to context: {contextName}");
            ContextChanged?.Invoke(this, contextName);

            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to switch to context: {contextName}", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return false;
        }
    }

    public async Task<ClusterHealthStatus> GetClusterHealthAsync()
    {
        var health = new ClusterHealthStatus
        {
            ClusterId = _currentCluster?.ContextName ?? "unknown",
            LastCheck = DateTime.Now,
            IsHealthy = true,
            Issues = new List<string>(),
            Metrics = new Dictionary<string, object>()
        };

        if (_client == null || _currentCluster == null)
        {
            health.IsHealthy = false;
            health.Issues.Add("Not connected to cluster");
            return health;
        }

        try
        {
            // Check nodes health
            var nodes = await _client.ListNodeAsync();
            var unhealthyNodes = nodes.Items.Where(n => GetNodeStatus(n) != "Ready").ToList();

            if (unhealthyNodes.Any())
            {
                health.IsHealthy = false;
                health.Issues.AddRange(unhealthyNodes.Select(n => $"Node {n.Metadata.Name} is not ready"));
            }

            // Check for failed pods
            var pods = await _client.ListPodForAllNamespacesAsync();
            var failedPods = pods.Items.Where(p => p.Status?.Phase == "Failed").ToList();

            if (failedPods.Any())
            {
                health.Issues.AddRange(failedPods.Select(p =>
                    $"Pod {p.Metadata.Name} in namespace {p.Metadata.NamespaceProperty} is failed"));
            }

            // Add metrics
            health.Metrics["TotalNodes"] = nodes.Items.Count;
            health.Metrics["ReadyNodes"] = nodes.Items.Count - unhealthyNodes.Count;
            health.Metrics["TotalPods"] = pods.Items.Count;
            health.Metrics["RunningPods"] = pods.Items.Count(p => p.Status?.Phase == "Running");
            health.Metrics["FailedPods"] = failedPods.Count;

            if (health.Issues.Count > 5)
            {
                health.IsHealthy = false;
            }
        }
        catch (Exception ex)
        {
            health.IsHealthy = false;
            health.Issues.Add($"Health check failed: {ex.Message}");
            _logService.LogError("Cluster health check failed", ex);
        }

        return health;
    }

    public async Task DisconnectAsync()
    {
        try
        {
            _client?.Dispose();
            _client = null;

            if (_currentCluster != null)
            {
                _currentCluster.IsConnected = false;
                _currentCluster = null;
            }

            _logService.LogInfo("Disconnected from cluster");
            ClusterDisconnected?.Invoke(this, EventArgs.Empty);
        }
        catch (Exception ex)
        {
            _logService.LogError("Error during disconnect", ex);
        }

        await Task.CompletedTask;
    }

    public async Task<ClusterInfo?> GetClusterInfoAsync()
    {
        if (_client == null || _currentCluster == null)
            return null;

        try
        {
            var nodes = await _client.ListNodeAsync();
            var namespaces = await _client.ListNamespaceAsync();

            // Update cluster info with additional details
            _currentCluster.Status = $"Nodes: {nodes.Items.Count}, Namespaces: {namespaces.Items.Count}";

            return _currentCluster;
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to get cluster info", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return _currentCluster;
        }
    }

    public async Task<IEnumerable<PodInfo>> GetPodsAsync(string? namespaceName = null)
    {
        if (_client == null)
            return Enumerable.Empty<PodInfo>();

        try
        {
            V1PodList pods;
            if (string.IsNullOrEmpty(namespaceName))
            {
                pods = await _client.ListPodForAllNamespacesAsync();
            }
            else
            {
                pods = await _client.ListNamespacedPodAsync(namespaceName);
            }

            return pods.Items.Select(pod => new PodInfo
            {
                Name = pod.Metadata.Name ?? "Unknown",
                Namespace = pod.Metadata.NamespaceProperty ?? "Unknown",
                Status = pod.Status?.Phase ?? "Unknown",
                Ready = GetPodReadyStatus(pod),
                Restarts = GetPodRestartCount(pod),
                Age = CalculateAge(pod.Metadata.CreationTimestamp),
                Node = pod.Spec?.NodeName ?? "Unknown",
                IP = pod.Status?.PodIP ?? "Unknown"
            });
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to get pods", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return Enumerable.Empty<PodInfo>();
        }
    }

    public async Task<IEnumerable<ServiceInfo>> GetServicesAsync(string? namespaceName = null)
    {
        if (_client == null)
            return Enumerable.Empty<ServiceInfo>();

        try
        {
            V1ServiceList services;
            if (string.IsNullOrEmpty(namespaceName))
            {
                services = await _client.ListServiceForAllNamespacesAsync();
            }
            else
            {
                services = await _client.ListNamespacedServiceAsync(namespaceName);
            }

            return services.Items.Select(service => new ServiceInfo
            {
                Name = service.Metadata.Name ?? "Unknown",
                Namespace = service.Metadata.NamespaceProperty ?? "Unknown",
                Type = service.Spec?.Type ?? "Unknown",
                ClusterIP = service.Spec?.ClusterIP ?? "Unknown",
                ExternalIP = GetExternalIP(service),
                Ports = GetServicePorts(service),
                Age = CalculateAge(service.Metadata.CreationTimestamp)
            });
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to get services", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return Enumerable.Empty<ServiceInfo>();
        }
    }

    public async Task<IEnumerable<NodeInfo>> GetNodesAsync()
    {
        if (_client == null)
            return Enumerable.Empty<NodeInfo>();

        try
        {
            var nodes = await _client.ListNodeAsync();

            return nodes.Items.Select(node => new NodeInfo
            {
                Name = node.Metadata.Name ?? "Unknown",
                Status = GetNodeStatus(node),
                Roles = GetNodeRoles(node),
                Age = CalculateAge(node.Metadata.CreationTimestamp),
                Version = node.Status?.NodeInfo?.KubeletVersion ?? "Unknown",
                InternalIP = GetNodeInternalIP(node),
                ExternalIP = GetNodeExternalIP(node),
                OSImage = node.Status?.NodeInfo?.OsImage ?? "Unknown",
                KernelVersion = node.Status?.NodeInfo?.KernelVersion ?? "Unknown",
                ContainerRuntime = node.Status?.NodeInfo?.ContainerRuntimeVersion ?? "Unknown"
            });
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to get nodes", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return Enumerable.Empty<NodeInfo>();
        }
    }

    public async Task<IEnumerable<string>> GetNamespacesAsync()
    {
        if (_client == null)
            return Enumerable.Empty<string>();

        try
        {
            var namespaces = await _client.ListNamespaceAsync();
            return namespaces.Items.Select(ns => ns.Metadata.Name ?? "Unknown");
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to get namespaces", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return Enumerable.Empty<string>();
        }
    }

    public async Task<string> GetPodLogsAsync(string podName, string namespaceName, int tailLines = 100)
    {
        if (_client == null)
            return string.Empty;

        try
        {
            var logs = await _client.ReadNamespacedPodLogAsync(podName, namespaceName, tailLines: tailLines);
            return logs;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to get logs for pod {podName}", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return $"Error retrieving logs: {ex.Message}";
        }
    }

    public async Task<bool> DeletePodAsync(string podName, string namespaceName)
    {
        if (_client == null)
            return false;

        try
        {
            await _client.DeleteNamespacedPodAsync(podName, namespaceName);
            _logService.LogInfo($"Deleted pod {podName} in namespace {namespaceName}");
            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to delete pod {podName}", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return false;
        }
    }

    public async Task<bool> RestartPodAsync(string podName, string namespaceName)
    {
        // Kubernetes doesn't have a direct restart command, so we delete the pod
        // and let the deployment/replicaset recreate it
        return await DeletePodAsync(podName, namespaceName);
    }

    public async Task<bool> ScaleDeploymentAsync(string deploymentName, string namespaceName, int replicas)
    {
        if (_client == null)
            return false;

        try
        {
            var deployment = await _client.ReadNamespacedDeploymentAsync(deploymentName, namespaceName);
            deployment.Spec.Replicas = replicas;

            await _client.ReplaceNamespacedDeploymentAsync(deployment, deploymentName, namespaceName);
            _logService.LogInfo($"Scaled deployment {deploymentName} to {replicas} replicas");
            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to scale deployment {deploymentName}", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return false;
        }
    }
}
