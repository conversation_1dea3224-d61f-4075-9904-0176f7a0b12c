using k8s;
using k8s.Models;
using DesK11.Core.Interfaces;
using DesK11.Models;
using System.IO;

namespace DesK11.Kubernetes;

public class KubernetesService : IKubernetesService
{
    private IKubernetes? _client;
    private ClusterInfo? _currentCluster;
    private KubernetesClientConfiguration? _currentConfig;
    private readonly ILogService _logService;

    public ClusterInfo? CurrentCluster => _currentCluster;
    public string? CurrentContext => _currentConfig?.CurrentContext;
    public bool IsConnected => _client != null && _currentCluster?.IsConnected == true;

    public event EventHandler<ClusterInfo>? ClusterConnected;
    public event EventHandler? ClusterDisconnected;
    public event EventHandler<string>? ErrorOccurred;
    public event EventHandler<string>? ContextChanged;

    public KubernetesService(ILogService logService)
    {
        _logService = logService;
    }

    public async Task<bool> ConnectAsync(string kubeConfigPath = "", string? contextName = null)
    {
        try
        {
            KubernetesClientConfiguration config;

            if (string.IsNullOrEmpty(kubeConfigPath))
            {
                config = KubernetesClientConfiguration.BuildDefaultConfig();
            }
            else
            {
                config = KubernetesClientConfiguration.BuildConfigFromConfigFile(kubeConfigPath);
            }

            // Switch to specific context if provided
            if (!string.IsNullOrEmpty(contextName))
            {
                // For context switching, we need to rebuild the config with the specific context
                config = KubernetesClientConfiguration.BuildConfigFromConfigFile(kubeConfigPath, contextName);
            }

            _client = new k8s.Kubernetes(config);
            _currentConfig = config;

            // Test connection by getting API resources
            await _client.CoreV1.GetAPIResourcesAsync();

            _currentCluster = new ClusterInfo
            {
                Name = config.CurrentContext ?? "default",
                Server = config.Host,
                Version = "v1.0.0", // Simplified for now
                ContextName = config.CurrentContext ?? "default",
                KubeConfigPath = kubeConfigPath,
                IsConnected = true,
                LastConnected = DateTime.Now
            };

            _logService.LogInfo($"Connected to cluster: {_currentCluster.Name} (Context: {_currentCluster.ContextName})");
            ClusterConnected?.Invoke(this, _currentCluster);

            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to connect to Kubernetes cluster", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return false;
        }
    }

    public async Task DisconnectAsync()
    {
        try
        {
            if (_client != null)
            {
                _client.Dispose();
                _client = null;
            }

            if (_currentCluster != null)
            {
                _currentCluster.IsConnected = false;
            }

            _currentConfig = null;
            _currentCluster = null;

            _logService.LogInfo("Disconnected from cluster");
            ClusterDisconnected?.Invoke(this, EventArgs.Empty);
        }
        catch (Exception ex)
        {
            _logService.LogError("Error during disconnect", ex);
        }
    }

    public async Task<ClusterInfo?> GetClusterInfoAsync()
    {
        return _currentCluster;
    }

    public async Task<IEnumerable<PodInfo>> GetPodsAsync(string? namespaceName = null)
    {
        try
        {
            if (_client == null) return new List<PodInfo>();

            V1PodList podList;
            if (string.IsNullOrEmpty(namespaceName))
            {
                podList = await _client.CoreV1.ListPodForAllNamespacesAsync();
            }
            else
            {
                podList = await _client.CoreV1.ListNamespacedPodAsync(namespaceName);
            }

            return podList.Items.Select(pod => new PodInfo
            {
                Name = pod.Metadata.Name,
                Namespace = pod.Metadata.NamespaceProperty,
                Status = pod.Status.Phase ?? "Unknown",
                Ready = "0/0", // Simplified
                Restarts = 0, // Simplified
                Age = (DateTime.Now - (pod.Metadata.CreationTimestamp ?? DateTime.Now)).ToString(@"dd\.hh\:mm\:ss"),
                Node = pod.Spec.NodeName ?? "Unknown"
            });
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to get pods", ex);
            return new List<PodInfo>();
        }
    }

    public async Task<IEnumerable<ServiceInfo>> GetServicesAsync(string? namespaceName = null)
    {
        try
        {
            if (_client == null) return new List<ServiceInfo>();

            V1ServiceList serviceList;
            if (string.IsNullOrEmpty(namespaceName))
            {
                serviceList = await _client.CoreV1.ListServiceForAllNamespacesAsync();
            }
            else
            {
                serviceList = await _client.CoreV1.ListNamespacedServiceAsync(namespaceName);
            }

            return serviceList.Items.Select(service => new ServiceInfo
            {
                Name = service.Metadata.Name,
                Namespace = service.Metadata.NamespaceProperty,
                Type = service.Spec.Type ?? "Unknown",
                ClusterIP = service.Spec.ClusterIP ?? "None",
                ExternalIP = "None", // Simplified
                Ports = "None", // Simplified
                Age = (DateTime.Now - (service.Metadata.CreationTimestamp ?? DateTime.Now)).ToString(@"dd\.hh\:mm\:ss")
            });
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to get services", ex);
            return new List<ServiceInfo>();
        }
    }

    public async Task<IEnumerable<NodeInfo>> GetNodesAsync()
    {
        try
        {
            if (_client == null) return new List<NodeInfo>();

            var nodeList = await _client.CoreV1.ListNodeAsync();

            return nodeList.Items.Select(node => new NodeInfo
            {
                Name = node.Metadata.Name,
                Status = "Ready", // Simplified
                Roles = "worker", // Simplified
                Age = (DateTime.Now - (node.Metadata.CreationTimestamp ?? DateTime.Now)).ToString(@"dd\.hh\:mm\:ss"),
                Version = node.Status.NodeInfo?.KubeletVersion ?? "Unknown",
                InternalIP = "Unknown", // Simplified
                ExternalIP = "Unknown" // Simplified
            });
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to get nodes", ex);
            return new List<NodeInfo>();
        }
    }

    public async Task<IEnumerable<string>> GetNamespacesAsync()
    {
        try
        {
            if (_client == null) return new List<string>();

            var namespaceList = await _client.CoreV1.ListNamespaceAsync();
            return namespaceList.Items.Select(ns => ns.Metadata.Name);
        }
        catch (Exception ex)
        {
            _logService.LogError("Failed to get namespaces", ex);
            return new List<string>();
        }
    }

    public async Task<string> GetPodLogsAsync(string podName, string namespaceName, int tailLines = 100)
    {
        try
        {
            if (_client == null) return "Not connected to cluster";

            var logsStream = await _client.CoreV1.ReadNamespacedPodLogAsync(podName, namespaceName, tailLines: tailLines);
            using var reader = new StreamReader(logsStream);
            return await reader.ReadToEndAsync();
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to get logs for pod {podName}", ex);
            return $"Error getting logs: {ex.Message}";
        }
    }

    public async Task<bool> DeletePodAsync(string podName, string namespaceName)
    {
        try
        {
            if (_client == null) return false;

            await _client.CoreV1.DeleteNamespacedPodAsync(podName, namespaceName);
            _logService.LogInfo($"Deleted pod {podName} in namespace {namespaceName}");
            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to delete pod {podName}", ex);
            return false;
        }
    }

    public async Task<bool> RestartPodAsync(string podName, string namespaceName)
    {
        // Simplified implementation - just delete the pod (if it's managed by a deployment, it will be recreated)
        return await DeletePodAsync(podName, namespaceName);
    }

    public async Task<bool> ScaleDeploymentAsync(string deploymentName, string namespaceName, int replicas)
    {
        try
        {
            if (_client == null) return false;

            var deployment = await _client.AppsV1.ReadNamespacedDeploymentAsync(deploymentName, namespaceName);
            deployment.Spec.Replicas = replicas;

            await _client.AppsV1.ReplaceNamespacedDeploymentAsync(deployment, deploymentName, namespaceName);
            _logService.LogInfo($"Scaled deployment {deploymentName} to {replicas} replicas");
            return true;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to scale deployment {deploymentName}", ex);
            return false;
        }
    }

    public async Task<IEnumerable<KubernetesContext>> GetAvailableContextsAsync(string kubeConfigPath = "")
    {
        try
        {
            // Simplified implementation - return empty list for now
            return new List<KubernetesContext>();
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to get available contexts", ex);
            return new List<KubernetesContext>();
        }
    }

    public async Task<IEnumerable<ContextInfo>> GetContextInfoAsync(string kubeConfigPath)
    {
        try
        {
            if (string.IsNullOrEmpty(kubeConfigPath) || !File.Exists(kubeConfigPath))
            {
                return new List<ContextInfo>();
            }

            // Load kubeconfig and parse contexts
            var kubeConfig = KubernetesClientConfiguration.LoadKubeConfig(kubeConfigPath);
            var contexts = new List<ContextInfo>();

            foreach (var context in kubeConfig.Contexts)
            {
                contexts.Add(new ContextInfo
                {
                    Name = context.Name,
                    Cluster = context.ContextDetails?.Cluster ?? "",
                    User = context.ContextDetails?.User ?? "",
                    Namespace = context.ContextDetails?.Namespace ?? "default",
                    IsActive = context.Name == kubeConfig.CurrentContext
                });
            }

            return contexts;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to get available contexts from {kubeConfigPath}", ex);
            return new List<ContextInfo>();
        }
    }

    public async Task<bool> SwitchContextAsync(string contextName)
    {
        try
        {
            // Simplified implementation
            _logService.LogInfo($"Context switch to {contextName} - not implemented yet");
            return false;
        }
        catch (Exception ex)
        {
            _logService.LogError($"Failed to switch context to {contextName}", ex);
            ErrorOccurred?.Invoke(this, ex.Message);
            return false;
        }
    }

    public async Task<ClusterHealthStatus> GetClusterHealthAsync()
    {
        try
        {
            if (_client == null)
            {
                return new ClusterHealthStatus
                {
                    ClusterId = _currentCluster?.Name ?? "unknown",
                    IsHealthy = false,
                    LastCheck = DateTime.Now,
                    Issues = new List<string> { "Not connected to cluster" }
                };
            }

            // Simple health check - try to get API resources
            await _client.CoreV1.GetAPIResourcesAsync();

            return new ClusterHealthStatus
            {
                ClusterId = _currentCluster?.Name ?? "unknown",
                IsHealthy = true,
                LastCheck = DateTime.Now,
                Issues = new List<string>()
            };
        }
        catch (Exception ex)
        {
            return new ClusterHealthStatus
            {
                ClusterId = _currentCluster?.Name ?? "unknown",
                IsHealthy = false,
                LastCheck = DateTime.Now,
                Issues = new List<string> { ex.Message }
            };
        }
    }
}
